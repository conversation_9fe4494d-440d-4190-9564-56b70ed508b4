import { IPintLinkDomainSettings } from '../interfaces/pint-link.interface';

export const EGNYTE_SU_DOMAIN_SETTINGS: IPintLinkDomainSettings = {
    file: {
        linkTypes: {
            direct: true,
        },
        default: 'direct',
    },
    folder: {
        linkTypes: {
            direct: true,
        },
        default: 'direct',
    },
    expiration: {
        default: null,
        max_allowed: null,
        upload_links: false,
    },
};
