import { ForbiddenException, Inject, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { MsAmUrlRepository } from 'ms-metaos-database/outlook';

@Injectable()
export class MsAmUrlService {
    constructor(
        @Inject('MS_AM_URL_CACHE') private msAmUrlCache: string[] | null,
        private readonly msAmUrlRepository: MsAmUrlRepository,
        private readonly logger: PinoLogger,
        private readonly configService: ConfigService
    ) {}

    public async validateAmUrl(amUrl: string): Promise<void> {
        await this.saveAmUrl(amUrl);
        const whitelistedAmUrls = this.configService.get<string[]>('amurlsWhitelist');
        if (whitelistedAmUrls?.length && !whitelistedAmUrls?.includes(amUrl)) {
            throw new ForbiddenException('Invalid amurl');
        }
    }

    private async saveAmUrl(value: string): Promise<void> {
        this.logger.info(`AMURL: Log amurl called with ${value}`);
        if (this.msAmUrlCache && this.msAmUrlCache.includes(value)) {
            this.logger.info(`AMURL: Amurl ${value} already in cache`);

            return;
        }

        this.msAmUrlCache = await this.msAmUrlRepository.addValue(value);
        this.logger.info(`AMURL: Saved amurl ${value} successfully`);
    }
}
