import { ShareLinkRequestDto } from '../../dtos/share-link-request.dto';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';
import { ShareLinkCreateItemModel } from './share-link-create-item.model';
import { SHARE_LINK_TYPE } from '../../enums/share-link-type.enum';
import { ShareLinkCreateExpirationModel } from './share-link-create-expiration.model';
import { Mappers } from '../../../common/utils/mappers';

export class ShareLinkCreateModel {
    @ValidateArgs()
    public static fromDto(data: ShareLinkRequestDto): ShareLinkCreateModel {
        return {
            expiration: ShareLinkCreateExpirationModel.fromDto(data.expiration),
            items: data.items.map((item) => ShareLinkCreateItemModel.fromDto(item)),
            shareType: Mappers.mapRawToModelLinkType(data.shareType),
            sendEmail: data.shareType === 'recipients' && !!data.sendEmail,
            ...(data.recipients && { recipients: data.recipients }),
            ...(data.password && { password: data.password }),
        };
    }

    public items: ShareLinkCreateItemModel[];

    public shareType: SHARE_LINK_TYPE;

    public expiration: ShareLinkCreateExpirationModel;

    public sendEmail: boolean;

    public recipients?: string[];

    public password?: string;
}
