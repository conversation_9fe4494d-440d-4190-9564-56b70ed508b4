import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { inspect } from 'util';
import { ERROR_CODE_TYPE } from 'ms-metaos-types/enums';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
    constructor(private readonly logger: Logger) {}

    public catch(exception: Record<string, unknown>, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();

        const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

        if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
            this.logger.error(`Error caught by filter: ${inspect(exception)}`);
        } else {
            this.logger.warn(`Error caught by filter: ${inspect(exception)}`);
        }

        const cause = exception?.cause instanceof Error ? exception.cause.name : ERROR_CODE_TYPE.UNKNOWN_ERROR;

        response.status(status).json({
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            message: exception?.message ?? 'Error occurred',
            code: cause,
        });
    }
}
