import { AuthEGReadModel } from './auth-eg-read.model';
import { UNIT_TEST_VARS } from '../../../common/utils/unit-test-vars';
import { Helpers } from '../../../common/utils/helpers';

export function getAuthEGReadModelMock(): AuthEGReadModel {
    return {
        userId: UNIT_TEST_VARS.userId,
        domain: Helpers.sanitizeDomain(UNIT_TEST_VARS.egDomain),
        accessToken: UNIT_TEST_VARS.egAccessToken,
        id: UNIT_TEST_VARS.egId,
        username: UNIT_TEST_VARS.egUsername,
        userType: UNIT_TEST_VARS.egUserType,
    };
}
