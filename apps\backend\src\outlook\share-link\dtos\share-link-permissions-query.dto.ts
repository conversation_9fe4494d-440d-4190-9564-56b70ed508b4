import { IsString, ValidateIf } from '@nestjs/class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class ShareLinkPermissionsQueryDto {
    @ApiPropertyOptional()
    @ValidateIf((obj: ShareLinkPermissionsQueryDto) => !obj.fileId)
    @IsString()
    public readonly folderId?: string;

    @ApiPropertyOptional()
    @ValidateIf((obj: ShareLinkPermissionsQueryDto) => !obj.folderId)
    @IsString()
    public readonly fileId?: string;
}
