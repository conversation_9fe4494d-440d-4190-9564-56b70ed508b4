import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from '@nestjs/class-validator';

export class MsAuthFinishQueryDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public error?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public state?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public code?: string;
}
