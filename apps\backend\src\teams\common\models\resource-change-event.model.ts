import utils from 'ms-metaos-utils';
import { TeamsHookRequestDto } from '../../incoming/hooks/dtos/teams-hook-request.dto';
import { IPublic } from '../utils/public.interface';
import { IUnpacked } from '../utils/unpacked.interface';
import { ChannelsHookRequestDto } from '../../incoming/hooks/dtos/channels-hook-request.dto';
import { MembersHookRequestDto } from '../../incoming/hooks/dtos/members-hook-request.dto';

class ResourceData {
    public id: string;
}

export class ResourceChangeEventReadModel {
    public static fromApiResponse(
        data:
            | IUnpacked<TeamsHookRequestDto['value']>
            | IUnpacked<ChannelsHookRequestDto['value'] | IUnpacked<MembersHookRequestDto['value']>>
    ): IPublic<ResourceChangeEventReadModel> {
        const [, id] = utils.getIdsFromResource(data.resource);

        return {
            resource: data.resource,
            resourceData: {
                id: data.resourceData?.id || id,
            },
            tenantId: data.tenantId,
            changeType: data.changeType,
            ...('createFolders' in data && { createFolders: data.createFolders }),
            ...('skipChildren' in data && { skipChildren: data.skipChildren }),
        };
    }

    public tenantId: string;

    public resourceData: ResourceData;

    public resource: string;

    public createFolders?: boolean;

    public skipChildren?: boolean;

    public changeType: 'created' | 'updated' | 'deleted';

    public resourceType?: 'team' | 'member' | 'channel';
}
