import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { AuthService } from '../../auth/services/auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'outlook-jwt') {
    constructor(
        configService: ConfigService,
        private readonly authService: AuthService
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('jwtTokenSecret'),
        });
    }

    async validate(payload: Record<string, string>): Promise<AuthReadModel> {
        return this.authService.getAuth(payload.userId);
    }
}
