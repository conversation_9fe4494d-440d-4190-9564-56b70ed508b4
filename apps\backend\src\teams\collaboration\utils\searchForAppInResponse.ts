import { EGAppsProviderResponse, EGAppsProviderIntegration } from '../../common/services/egnyte/egnyte.types';

export function searchForAppInResponse(appsProviderResponse: EGAppsProviderResponse, coeditAppIds: string[]) {
    const searchFunction = (app: EGAppsProviderIntegration) => coeditAppIds.includes(app.appId);

    const foundAppInApps = appsProviderResponse.apps.find(searchFunction);
    if (foundAppInApps) {
        return foundAppInApps;
    }

    const foundAppInGroups = appsProviderResponse.appsForGroups.find(searchFunction);
    if (foundAppInGroups) {
        return foundAppInGroups;
    }

    return null;
}
