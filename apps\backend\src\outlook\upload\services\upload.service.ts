import { InjectQueue } from '@nestjs/bullmq';
import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { Queue } from 'bullmq';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { utils } from '@integrations/pint-common';
import { inspect } from 'util';
import {
    FileUploadCreateModel,
    FileUploadReadModel,
    FileUploadRepository,
    FileUploadUpdateModel,
    SettingsRepository,
    UploadCreateModel,
    UploadReadModel,
    UploadRepository,
} from 'ms-metaos-database/outlook';
import { BULL_JOB, BULL_QUEUE } from '../../common/consts/bull-queue.const';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { FilePickerItemReadModel } from '../../file-picker/models/file-picker-item/file-picker-item-read.model';

@Injectable()
export class UploadService {
    constructor(
        @InjectQueue(BULL_QUEUE.ATTACHMENT) private readonly attachmentQueue: Queue,
        private readonly configService: ConfigService,
        private readonly settingsRepository: SettingsRepository,
        private readonly uploadRepository: UploadRepository,
        private readonly egnyteClientService: EgnyteClientService,
        private readonly fileUploadRepository: FileUploadRepository,
        private readonly logger: PinoLogger
    ) {}

    public async startUpload(uploadCreateModels: UploadCreateModel[]): Promise<Array<{ jobId: string }>> {
        const uploadJobs = await Promise.all(
            uploadCreateModels.map((attachment) => this.startSingleUpload(attachment))
        );

        this.logger.info(
            `All jobs successfully created - number of jobs: ${uploadJobs.length}. MessageId ${uploadCreateModels[0].msMessageId}`
        );

        return uploadJobs;
    }

    public async getUploadFolder(userId: string): Promise<string> {
        try {
            const userSettings = await this.settingsRepository.getSettings(userId);

            if (!userSettings?.egUploadFolder?.folderId) {
                throw new BadRequestException('Upload folder not selected');
            }

            const { path } = await this.egnyteClientService.getFolderDetailsById(userSettings.egUploadFolder.folderId, {
                withChildren: false,
            });
            if (!path) {
                throw new BadRequestException("Specified upload folder doesn't exist");
            }

            return path;
        } catch (error) {
            this.logger.error(error);
            throw error;
        }
    }

    public async checkAttachmentStatus(
        userId: string,
        msMessageId: string,
        uploadId?: string
    ): Promise<UploadReadModel[]> {
        return this.uploadRepository.findUploadsByMessageId({ userId, msMessageId, uploadId });
    }

    public async deleteUploads(userId: string, msMessageId: string): Promise<void> {
        try {
            await this.uploadRepository.removeAllUploads({ userId, msMessageId });
            this.logger.info(`Deleted upload records`);
        } catch (e) {
            this.logger.error(`Delete upload records failed. Error ${inspect(e)}`);
            throw new InternalServerErrorException(`Delete uploads error`);
        }
    }

    public async uploadFile(
        fileUploadCreateModel: FileUploadCreateModel,
        fileStream: NodeJS.ReadableStream,
        originalName: string
    ): Promise<FilePickerItemReadModel | null> {
        await this.fileUploadRepository.addUpload(fileUploadCreateModel);
        const egFolderContent = await this.egnyteClientService.getFolderDetailsByPath(
            fileUploadCreateModel.uploadFolderPath,
            {
                withChildren: true,
            }
        );

        const fileName = utils.file.addFileNameVersionSuffix(originalName, egFolderContent.items ?? []);
        const filePath = `${fileUploadCreateModel.uploadFolderPath}/${fileName}`;

        const result = await this.egnyteClientService.uploadFile(filePath, fileStream);

        const fileUploadReadModel = await this.fileUploadRepository.getUpload(
            fileUploadCreateModel.userId,
            fileUploadCreateModel.uploadKey
        );
        if (fileUploadReadModel.deleteOnDone) {
            await this.egnyteClientService.deleteFile(filePath);
            this.logger.warn(`File upload process - deleteOnDone: ${filePath}`);

            return null;
        }

        return result;
    }

    public async markFileToDelete(fileUploadUpdateModel: FileUploadUpdateModel): Promise<FileUploadReadModel> {
        return this.fileUploadRepository.updateUpload(fileUploadUpdateModel);
    }

    private async startSingleUpload(uploadSingleCreateModel: UploadCreateModel): Promise<{ jobId: string }> {
        const uploadAttemptsMaxQuantity = this.configService.get('uploadAttemptsMaxQuantity', 1);
        const backoffRetriesDelay = this.configService.get('backoffRetriesDelay', 1100);

        await this.uploadRepository.addUpload(uploadSingleCreateModel);

        const reportData = {
            startUploadTime: Date.now(),
        };

        try {
            const createdJob = await this.attachmentQueue.add(
                BULL_JOB.ATTACHMENT_UPLOAD,
                UploadCreateModel.fromModelWithReport(uploadSingleCreateModel, reportData),
                {
                    attempts: uploadAttemptsMaxQuantity,
                    backoff: backoffRetriesDelay,
                }
            );

            return { jobId: createdJob.id };
        } catch (e) {
            this.logger.error(
                `Create job error. Attachment id: ${uploadSingleCreateModel.msAttachmentId}. Error ${inspect(e)}`
            );

            await this.uploadRepository.removeUpload({
                userId: uploadSingleCreateModel.userId,
                msMessageId: uploadSingleCreateModel.msMessageId,
                msAttachmentId: uploadSingleCreateModel.msAttachmentId,
            });

            throw new InternalServerErrorException(
                `Create job error for attachment ${uploadSingleCreateModel.msAttachmentId}`
            );
        }
    }
}
