import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from '@nestjs/class-validator';
import { SearchV2ReqBody } from '@integrations/egnyte-ts-sdk';
import { SORT_BY, SORT_DIRECTION } from './file-picker-folder-query.dto';

export class FilePickerSearchQueryDto {
    public static toApiQuery(query: FilePickerSearchQueryDto): SearchV2ReqBody {
        return {
            query: query.query,
            type: 'FILE',
            ...(query.folder && { folder: query.folder }),
            ...(query.itemsToReturn && { count: Number(query.itemsToReturn) || 20 }),
            ...(query.offset && { offset: Number(query.offset) }),
            ...(['name', 'date'].includes(query.sortBy) && {
                sort_by: SORT_BY[query.sortBy],
                sort_direction: SORT_DIRECTION[query.sortDirection],
            }),
        };
    }

    @ApiProperty()
    @IsString()
    public query: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    public folder?: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    public itemsToReturn?: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    public offset?: string;

    @ApiPropertyOptional({ type: String, enum: SORT_BY })
    @IsEnum(SORT_BY)
    @IsOptional()
    public sortBy?: string;

    @ApiPropertyOptional({ type: String, enum: SORT_DIRECTION })
    @IsEnum(SORT_DIRECTION)
    @IsOptional()
    public sortDirection?: string;
}
