import { IsBoolean, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from '@nestjs/class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ShareLinkExpirationNestedDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsBoolean()
    isActive: boolean;

    @ApiPropertyOptional({ type: String, enum: ['click', 'days', 'week', 'months'], example: 'click' })
    @IsOptional()
    @IsString()
    unitType?: string; // "click" | "days" | "week" | "months",

    @ApiPropertyOptional({ example: 500 })
    @ValidateIf((expiration: ShareLinkExpirationNestedDto) => expiration.isActive)
    @Min(1)
    @Max(10000)
    value?: number;

    @ApiPropertyOptional({ example: 'Europe/Warsaw' })
    @ValidateIf(
        (expiration: ShareLinkExpirationNestedDto) =>
            expiration.isActive && ['days', 'week', 'months'].includes(expiration.unitType)
    )
    @IsString()
    timezone?: string;
}
