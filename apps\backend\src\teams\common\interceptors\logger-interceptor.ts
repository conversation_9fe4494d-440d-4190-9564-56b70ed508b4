import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { Observable } from 'rxjs';
import { getLoggerDataFromRequest } from '../config/module-config';

@Injectable()
export class LoggerInterceptor implements NestInterceptor {
    constructor(private readonly logger: PinoLogger) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const data = getLoggerDataFromRequest(request);

        this.logger.assign(data);

        return next.handle();
    }
}
