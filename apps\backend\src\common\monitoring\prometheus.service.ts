import { Injectable } from '@nestjs/common';
import * as Prometheus from '@integrations/pint-prometheus';
import utils from 'ms-metaos-utils';
import express from 'express';
import { MetricsMiddlewareFactory } from './metrics.types';

@Injectable()
export class PrometheusService implements MetricsMiddlewareFactory {
    constructor() {
        Prometheus.initialize({});
    }

    public getMiddleware(): (req: express.Request, res: express.Response, next: express.NextFunction) => void {
        return Prometheus.getMiddleware({
            getPath: (req: express.Request) => utils.sanitizer.normalizePath(req, { valueMasks: [/(\w|\d|-)=$/] }),
            includePath: true,
            includeMethod: true,
        });
    }
}
