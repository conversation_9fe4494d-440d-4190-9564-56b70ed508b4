import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import {
    DatabaseAuthModule,
    DatabaseAuthSetupModule,
    DatabaseAdminAuthModule,
    DatabaseAdminAuthSetupModule,
} from 'ms-metaos-database/teams';
import { MicrosoftAuthClientModule } from 'ms-metaos-modules';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { EgnytePrivateClientModule } from '../common/services/egnyte-private-client/egnyte-private-client.module';
import { EgnyteModule } from '../common/services/egnyte/egnyte.module';
import { ResourceSettingsModule } from '../tenant/resource-settings/resource-settings.module';
import { UrlBuilderModule } from '../common/services/url-builder/url-builder.module';

@Module({
    imports: [
        JwtModule.registerAsync({
            useFactory: (configService: ConfigService) => ({
                secret: configService.get('jwtTokenSecret'),
                signOptions: {
                    expiresIn: configService.get('jwtTokenExpiration'),
                    algorithm: 'HS512',
                },
            }),
            inject: [ConfigService],
        }),
        DatabaseAuthModule,
        DatabaseAuthSetupModule,
        DatabaseAdminAuthModule,
        DatabaseAdminAuthSetupModule,
        MicrosoftAuthClientModule,
        EgnyteModule,
        EgnytePrivateClientModule,
        ResourceSettingsModule,
        UrlBuilderModule,
    ],
    controllers: [AuthController],
    providers: [AuthService],
    exports: [AuthService],
})
export class AuthModule {}
