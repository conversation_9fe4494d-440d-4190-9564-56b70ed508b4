import { HttpException, Injectable } from '@nestjs/common';
import {
    EgnyteClient,
    GettingUserInfoRes,
    ListFileRes,
    ListFolderRes,
    ShowLinkDetailsRes,
} from '@integrations/egnyte-ts-sdk';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class EgnyteLinksHelperService {
    private directLinkFileRegExp = /^https:\/\/.{1,}\/navigate\/file\/[a-zA-Z0-9-]{1,}(\/)?/;

    private directLinkFolderReqExp = /^https:\/\/.{1,}\/navigate\/folder\/[a-zA-Z0-9-]{1,}(\/)?/;

    private publicLinkRegExp = /^https:\/\/.{1,}\/[d,f]l\/[a-zA-Z0-9]{1,}(\/)?/;

    private directDownloadRegExp = /^https:\/\/.{1,}\/dd\/[a-zA-Z0-9]{1,}(\/)?/;

    constructor(private logger: PinoLogger) {}

    public isFileDirectLink(link: string): boolean {
        return this.directLinkFileRegExp.test(link);
    }

    public isFolderDirectLink(link: string): boolean {
        return this.directLinkFolderReqExp.test(link);
    }

    public isPublicLink(link: string): boolean {
        return this.publicLinkRegExp.test(link) || this.directDownloadRegExp.test(link);
    }

    public getLinkIdFromURL(url: string): string {
        if (this.isFileDirectLink(url) || this.isFolderDirectLink(url)) {
            return url.match(/navigate\/(file|folder)\/([a-zA-Z0-9-]+)/)[2];
        }

        return url.match(/[d,f][d,l]\/([^/]+)/)[1];
    }

    public prepareOpenInEgnyteUrl(fromUrl: string, type: 'folder' | 'file', id: string): string {
        return `${new URL(fromUrl).origin}/navigate/${type}/${id}`;
    }

    public async getLinkInfo({ link, egnyteInstance }: { link: string; egnyteInstance: EgnyteClient }): Promise<{
        url: string;
        user: GettingUserInfoRes;
        item: ListFileRes | ListFolderRes;
    }> {
        try {
            const linkId = this.getLinkIdFromURL(link);
            const user = await egnyteInstance.api('/pubapi/v1/userinfo').get<GettingUserInfoRes>();

            if (this.isFileDirectLink(link)) {
                this.logger.info(`[EgnyteLinksHelperService/getLinkInfo] Link: ${link} is of type file "direct link"`);

                const fileInfo = await egnyteInstance.api(`/pubapi/v1/fs/ids/file/${linkId}`).get<ListFileRes>();
                const egnyteUrl = this.prepareOpenInEgnyteUrl(link, 'file', fileInfo.group_id);

                return { item: fileInfo, url: egnyteUrl, user };
            }

            if (this.isFolderDirectLink(link)) {
                this.logger.info(
                    `[EgnyteLinksHelperService/getLinkInfo] Link: ${link} is of type folder "direct link"`
                );

                const folderInfo = await egnyteInstance.api(`/pubapi/v1/fs/ids/folder/${linkId}`).get<ListFolderRes>();
                const egnyteUrl = this.prepareOpenInEgnyteUrl(link, 'folder', folderInfo.folder_id);

                return { item: folderInfo, url: egnyteUrl, user };
            }

            if (this.isPublicLink(link)) {
                this.logger.info(`[EgnyteLinksHelperService/getLinkInfo] Link: ${link} is of type "public link"`);
                const linkInfo = await egnyteInstance.api(`/pubapi/v1/links/${linkId}`).get<ShowLinkDetailsRes>();

                const itemInfo = await egnyteInstance
                    .api(
                        `/pubapi/v1/fs/${linkInfo.path
                            .replace(/^\//, '')
                            .split('/')
                            .map((item) => encodeURIComponent(item))
                            .join('/')}`
                    )
                    .get<ListFolderRes | ListFileRes>();
                const egnyteUrl = this.prepareOpenInEgnyteUrl(
                    link,
                    itemInfo.is_folder ? 'folder' : 'file',
                    itemInfo.is_folder ? (itemInfo as ListFolderRes).folder_id : (itemInfo as ListFileRes).group_id
                );

                return {
                    item: itemInfo,
                    url: egnyteUrl,
                    user,
                };
            }
        } catch (error) {
            this.logger.error(error, '[EgnyteLinksHelperService/getLinkInfo] Checking type of link failed');

            throw new HttpException(error.message || `Can not get link info for url ${link}`, error.statusCode ?? 500);
        }
    }
}
