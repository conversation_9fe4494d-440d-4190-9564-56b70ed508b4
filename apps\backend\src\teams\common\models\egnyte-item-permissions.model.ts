import { GetEffectivePermissionForAUserRes, PermissionEnum } from '@integrations/egnyte-ts-sdk';
import { PERMISSION_ACTION } from 'ms-metaos-types/enums';
import { IPublic } from '../utils/public.interface';

const actionsByPermissionMapper = {
    [PermissionEnum.VIEWER_ONLY]: [PERMISSION_ACTION.PREVIEW_FILES_IN_WEB_UI],
    [PermissionEnum.VIEWER]: [PERMISSION_ACTION.PREVIEW_FILES_IN_WEB_UI, PERMISSION_ACTION.DOWNLOAD_READ_FILES_FOLDERS],
    [PermissionEnum.EDITOR]: [
        PERMISSION_ACTION.PREVIEW_FILES_IN_WEB_UI,
        PERMISSION_ACTION.DOWNLOAD_READ_FILES_FOLDERS,
        PERMISSION_ACTION.COPY_FILES_FOLDERS,
        PERMISSION_ACTION.UPLOAD_EDIT_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_SUB_FOLDERS,
        PERMISSION_ACTION.RENAME_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_UPLOAD_LINKS,
    ],
    [PermissionEnum.FULL]: [
        PERMISSION_ACTION.PREVIEW_FILES_IN_WEB_UI,
        PERMISSION_ACTION.DOWNLOAD_READ_FILES_FOLDERS,
        PERMISSION_ACTION.COPY_FILES_FOLDERS,
        PERMISSION_ACTION.UPLOAD_EDIT_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_SUB_FOLDERS,
        PERMISSION_ACTION.RENAME_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_UPLOAD_LINKS,
        PERMISSION_ACTION.MOVE_FILES_FOLDERS,
        PERMISSION_ACTION.DELETE_FILES_FOLDERS,
    ],
    [PermissionEnum.OWNER]: [
        PERMISSION_ACTION.PREVIEW_FILES_IN_WEB_UI,
        PERMISSION_ACTION.DOWNLOAD_READ_FILES_FOLDERS,
        PERMISSION_ACTION.COPY_FILES_FOLDERS,
        PERMISSION_ACTION.UPLOAD_EDIT_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_SUB_FOLDERS,
        PERMISSION_ACTION.RENAME_FILES_FOLDERS,
        PERMISSION_ACTION.CREATE_UPLOAD_LINKS,
        PERMISSION_ACTION.MOVE_FILES_FOLDERS,
        PERMISSION_ACTION.DELETE_FILES_FOLDERS,
        PERMISSION_ACTION.EDIT_FOLDER_SHARING,
    ],
};

export class EgnyteItemPermissionsReadModel {
    public static fromApiResponse(
        response: GetEffectivePermissionForAUserRes
    ): IPublic<EgnyteItemPermissionsReadModel> {
        return {
            permissions: !(response.permission in PermissionEnum) ? null : (response.permission as PermissionEnum),
            permissionActions: actionsByPermissionMapper[response.permission as PermissionEnum],
        };
    }

    public permissions: PermissionEnum | null;

    public permissionActions: PERMISSION_ACTION[];
}
