import { GettingUserInfoRes, ListFileRes, ListFolderRes } from '@integrations/egnyte-ts-sdk';
import { IPublic } from '../utils/public.interface';
import { EgnyteItemReadModel } from './egnyte-item.model';
import { EgnyteUserReadModel } from './egnyte-user.model';

export class EgnyteLinkInfoReadModel {
    public static fromApiResponse(response: {
        url: string;
        domain: string;
        user: GettingUserInfoRes;
        item: ListFileRes | ListFolderRes;
    }): IPublic<EgnyteLinkInfoReadModel> {
        return {
            url: response.url,
            domain: response.domain,
            user: EgnyteUserReadModel.fromApiResponse(response.user),
            item: EgnyteItemReadModel.fromApiResponse(response.item),
        };
    }

    public url: string;

    public domain: string;

    public item: EgnyteItemReadModel;

    public user: EgnyteUserReadModel;
}
