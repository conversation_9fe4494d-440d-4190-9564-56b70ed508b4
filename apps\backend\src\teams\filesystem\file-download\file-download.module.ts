import { Module } from '@nestjs/common';
import { FileDownloadController } from './controllers/file-download.controller';
import { FileDownloadService } from './services/file-download.service';
import { EgnyteModule } from '../../common/services/egnyte/egnyte.module';

@Module({
    imports: [EgnyteModule],
    controllers: [FileDownloadController],
    providers: [FileDownloadService],
})
export class FileDownloadModule {}
