import { ApiProperty } from '@nestjs/swagger';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class AuthRedirectResponseDto {
    @ValidateArgs()
    public static fromData(statusCode: number, url: string): AuthRedirectResponseDto {
        return {
            statusCode,
            url,
        };
    }

    @ApiProperty({ example: 302 })
    public statusCode: number;

    @ApiProperty()
    public url: string;
}
