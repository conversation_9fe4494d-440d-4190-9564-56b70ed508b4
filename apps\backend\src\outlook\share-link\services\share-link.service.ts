import { HttpException, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import * as pintlink from '@integrations/pint-link';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { DomainEnvMetadata } from '../../common/utils/domain-env-metadata';
import { ShareLinkCreateModel } from '../models/share-link/share-link-create.model';
import { ShareLinkReadModel } from '../models/share-link/share-link-read.model';
import { ShareLinkUsersReadModel } from '../models/share-link-recipients/share-link-users-read.model';
import { SHARE_LINK_ITEM_TYPE } from '../enums/share-link-item-type.enum';
import { ShareLinkPermissionsReadModel } from '../models/share-link-permissions/share-link-permissions-read.model';

@Injectable()
export class ShareLinkService {
    constructor(
        private readonly egnyteClientService: EgnyteClientService,
        private readonly domainEnvMetadata: DomainEnvMetadata,
        private readonly logger: PinoLogger
    ) {}

    public async createShareLinkByFileId(
        shareLinkCreateModel: ShareLinkCreateModel
    ): Promise<Array<ShareLinkReadModel>> {
        const egnyteInstance = await this.egnyteClientService.getInstance();

        return Promise.all(
            shareLinkCreateModel.items.map(async (item) => {
                try {
                    const linkRequestData = {
                        id: item.id,
                        type: item.isFolder ? SHARE_LINK_ITEM_TYPE.FOLDER : SHARE_LINK_ITEM_TYPE.FILE,
                        accessibility: shareLinkCreateModel.shareType,
                        ...(shareLinkCreateModel.expiration.isActive && {
                            expiration: {
                                default: {
                                    unit: shareLinkCreateModel.expiration.unitType,
                                    value: shareLinkCreateModel.expiration.value,
                                },
                            },
                        }),
                        ...(shareLinkCreateModel.recipients && { recipients: shareLinkCreateModel.recipients }),
                        ...(shareLinkCreateModel.sendEmail && { send_email: true }),
                        ...(shareLinkCreateModel.password && { password: shareLinkCreateModel.password }),
                        ...(shareLinkCreateModel.expiration.timezone && {
                            timezone: shareLinkCreateModel.expiration.timezone,
                        }),
                    };
                    const result = await pintlink.createLink(egnyteInstance, linkRequestData, { debug: () => null });
                    this.logger.info(
                        `Share-Link created successfully. Item id: ${item.id}. Expiration: ${JSON.stringify(
                            shareLinkCreateModel.expiration
                        )}. Accessibility: ${shareLinkCreateModel.shareType}. Recipients: ${
                            shareLinkCreateModel.recipients ? shareLinkCreateModel.recipients.join(', ') : 'n/a'
                        }.`
                    );

                    return ShareLinkReadModel.fromResponse(result, linkRequestData.type);
                } catch (e) {
                    this.logger.error(`Create link error: ${e.message}`);
                    throw new HttpException(`Create link error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
                }
            })
        );
    }

    public async searchForRecipients(query: string): Promise<ShareLinkUsersReadModel> {
        const egUsersInfo = await this.egnyteClientService.searchForUsers(query);
        egUsersInfo.items = egUsersInfo.items.filter((user) => !user.emailChangePending && user.active);

        return egUsersInfo;
    }

    public async getPermissions({
        folderId,
        fileId,
    }: {
        folderId?: string;
        fileId?: string;
    }): Promise<ShareLinkPermissionsReadModel> {
        return this.egnyteClientService.getPermissionsForItemId({ folderId, fileId });
    }

    private async getDomainEnv(egDomain: string) {
        try {
            return this.domainEnvMetadata.getDomainEnv(egDomain);
        } catch (e) {
            this.logger.error(`Create link error: ${e.message}`);
            throw new HttpException(`Create link error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }
}
