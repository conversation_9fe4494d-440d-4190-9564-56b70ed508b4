import { ApiProperty } from '@nestjs/swagger';
import { SettingsReadMetadataModel } from 'ms-metaos-database/outlook';

export class SettingsGetEgMetadataResponseNestedDto {
    public static fromResponse(data: SettingsReadMetadataModel): SettingsGetEgMetadataResponseNestedDto {
        return {
            isNamespaceAdded: data.isNamespaceAdded,
            isSavingEnabled: data.isSavingEnabled,
        };
    }

    @ApiProperty()
    public isSavingEnabled: boolean;

    @ApiProperty()
    public isNamespaceAdded: boolean;
}
