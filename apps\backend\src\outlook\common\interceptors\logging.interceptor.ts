import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { tap } from 'rxjs/operators';
import { utils } from '@integrations/pint-common';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { IgnoreLoggingOptions } from '../decorators/ignore-logging.decorator';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
    constructor(
        private readonly logger: PinoLogger,
        private reflector: Reflector
    ) {}

    private static createPlatformInfoValue(req: Request): string {
        const outlookPlatform = `outlook platform: ${req.headers['x-outlook-platform'] || 'n/a'}`;
        const outlookVersion = `outlook version: ${req.headers['x-outlook-version'] || 'n/a'}`;
        const addinVersion = `addin version: ${req.headers['x-addin-version'] || 'n/a'}`;

        return `${outlookPlatform} - ${outlookVersion} - ${addinVersion}`;
    }

    intercept(context: ExecutionContext, next: CallHandler): Observable<Request & { user?: AuthReadModel }> {
        const req = context.switchToHttp().getRequest<Request & { user?: AuthReadModel }>();
        const loggerLimitations = this.reflector.getAllAndMerge('IgnoreLogging', [
            context.getHandler(),
            context.getClass(),
        ]);

        if (loggerLimitations.includes(IgnoreLoggingOptions.ALL)) {
            return next.handle();
        }

        const egnyteRequestId = req.headers['x-egnyte-request-id'] || 'n/a';
        const requestPath = utils.normalizePath(req, { valueMasks: [/(\w|\d|-)=$/] });

        this.logger.assign({
            platformInfo: LoggingInterceptor.createPlatformInfoValue(req),
            ...(requestPath && { url: requestPath }),
            ...(req.method && { requestMethod: req.method }),
            ...(egnyteRequestId && { egnyteRequestId }),
            ...(req.headers['user-agent'] && { useragent: req.headers['user-agent'] }),
        });

        if (!loggerLimitations.includes(IgnoreLoggingOptions.REQ_USER)) {
            this.logger.assign({
                userId: req.user?.userId || 'n/a',
                domain: req.user?.egnyte?.domain || 'n/a',
                msTenantId: req.user?.microsoft?.tenantId || 'n/a',
            });
        }

        if (loggerLimitations.includes(IgnoreLoggingOptions.START_STOP)) {
            return next.handle();
        }

        this.logger.info(`start [${req.method}]`);

        return next.handle().pipe(
            tap(() => {
                this.logger.info(`end [${context.switchToHttp().getResponse().statusCode}]`);
            })
        );
    }
}
