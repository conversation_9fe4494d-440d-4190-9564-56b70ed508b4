import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NextFunction, Request, Response } from 'express';
import { ReportingService } from '../services/reporting/reporting.service';

@Injectable()
export class ReportingMiddleware implements NestMiddleware {
    private readonly reportingMiddleware: (req: Request, res: Response, next: NextFunction) => void;

    constructor(configService: ConfigService, reportingService: ReportingService) {
        if (configService.get('enableReporting')) {
            this.reportingMiddleware = reportingService.getMiddleware();
        }
    }

    public use(req: Request, res: Response, next: NextFunction): void {
        if (this.reportingMiddleware) {
            this.reportingMiddleware(req, res, next);
        }
    }
}
