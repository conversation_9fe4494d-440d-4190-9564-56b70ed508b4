import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { getReportingServiceMock } from '../../common/services/reporting/reporting.service.mock';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { ShareLinkController } from './share-link.controller';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { ShareLinkService } from '../services/share-link.service';
import { getShareLinkServiceMock } from '../services/share-link.service.mock';
import { getShareLinkUsersReadModelMock } from '../models/share-link-recipients/share-link-users-read.mock';
import { ShareLinkRecipientsResponseDto } from '../dtos/share-link-recipients-response.dto';
import { getShareLinkPermissionsReadModelMock } from '../models/share-link-permissions/share-link-permissions-read.mock';
import { ShareLinkPermissionsResponseDto } from '../dtos/share-link-permissions-response.dto';

describe('ShareLinkController', () => {
    const argsMock = {
        itemId: 'testItemId',
        searchRecipientsQueryString: 'testSearchRecipientsQueryString',
    };

    let testingModule: TestingModule;

    let shareLinkService: ShareLinkService;
    let shareLinkController: ShareLinkController;

    beforeEach(async () => {
        jest.resetAllMocks();

        testingModule = await Test.createTestingModule({
            providers: [
                { provide: ShareLinkService, useValue: getShareLinkServiceMock() },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                { provide: ReportingService, useValue: getReportingServiceMock() },
            ],
            controllers: [ShareLinkController],
        }).compile();

        shareLinkService = testingModule.get<ShareLinkService>(ShareLinkService);
        shareLinkController = testingModule.get<ShareLinkController>(ShareLinkController);
    });

    describe('createShareLink', () => {
        it.todo('Create createShareLink method tests');
    });

    describe('getPermissions', () => {
        it('should successfully get permissions and map the model to dto, when defaults and max expiration values are specified for the domain', async () => {
            const model = getShareLinkPermissionsReadModelMock();
            const expected = ShareLinkPermissionsResponseDto.fromResponse(model);
            const result = await shareLinkController.getPermissions({ folderId: argsMock.itemId });

            expect(result).toEqual(expected);
        });

        it('should successfully get permissions and map the model to dto, when defaults and max expiration values are not set', async () => {
            const model = getShareLinkPermissionsReadModelMock();
            model.expiration.maxAllowed = {
                direct: null,
                anyone: null,
                domain: null,
                recipients: null,
                password: null,
            };
            model.expiration.default = { direct: null, anyone: null, domain: null, recipients: null, password: null };

            (shareLinkService.getPermissions as jest.Mock).mockResolvedValue(model);

            const expected = ShareLinkPermissionsResponseDto.fromResponse(model);
            const result = await shareLinkController.getPermissions({ folderId: argsMock.itemId });

            expect(result).toEqual(expected);
        });
    });
    describe('searchRecipients', () => {
        it('should successfully get permissions and map the model to dto', async () => {
            const model = getShareLinkUsersReadModelMock();

            const result = await shareLinkController.searchRecipients(argsMock.searchRecipientsQueryString);

            const expected = ShareLinkRecipientsResponseDto.fromResponse(model);

            expect(result).toEqual(expected);
        });
    });
});
