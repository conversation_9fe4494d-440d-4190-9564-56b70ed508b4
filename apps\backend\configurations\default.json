{"appName": "ms-metaos-backend", "basePath": "/ms-metaos", "clientPath": "/client", "teamsClientPath": "/client/teams", "outlookClientPath": "/client/outlook", "jwtTokenExpiration": "5m", "adminJwtTokenExpiration": "30m", "gracefulShutdownDelayMs": 2000, "enableSwagger": true, "egAppsProviderUrl": {"us": "https://us-partner-integrations.egnyte.com/apps-provider/v2/apps", "eu": "https://partner-integrations.egnyte.com/apps-provider/v2/apps", "me": "https://me-partner-integrations.egnyte.com/apps-provider/v2/apps", "staging": "https://staging-partner-integrations.egnyte.com/apps-provider/v2/apps", "qa": "https://integrations-staging.qa-egnyte.com/apps-provider/v2/apps", "azure-qa": "https://integrations-azure.qa-egnyte.com/apps-provider/v2/apps"}, "egPermissionsManagement": {"linkSourceIdSuffix": ""}, "prometheus": {"enabled": true}, "reporting": {"enabled": true, "appId": "msmetaos"}, "searchQueryFallbackValue": "* OR NOT *", "uploadAttemptsMaxQuantity": 4, "backoffRetriesDelay": 3000, "authSetupExpiration": "10m", "attachmentUploadJobsMaxQuantity": 1, "attachmentUploadJobsMaxDuration": 600, "attachmentUploadJobsWaitForOtherJobsMaxDuration": 1, "attachmentUploadExpiration": "11m", "fileUploadExpiration": "30m", "msAuthTokenRefreshThreshold": 600, "enableMetrics": true, "enableReporting": true, "supportedShareLinks": ["anyone", "domain", "direct", "recipients", "password"], "amurlsWhitelist": []}