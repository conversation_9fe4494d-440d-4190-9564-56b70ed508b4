// eslint-disable-next-line max-classes-per-file
import { ShareLinkPermissionsReadModel } from './share-link-permissions-read.model';
import { SHARE_LINK_TYPE } from '../../enums/share-link-type.enum';
import { SHARE_LINK_EXPIRATION_UNIT } from '../../enums/share-link-expiration-unit.enum';

function getShareLinkExpirationMock(): ShareLinkPermissionsReadModel['expiration'] {
    const defaultValue = {
        unit: SHARE_LINK_EXPIRATION_UNIT.DAYS as const,
        value: 10,
    };
    const maxAllowed = {
        expiryTimeValue: 90,
        expiryTimeUnit: SHARE_LINK_EXPIRATION_UNIT.DAYS as const,
        expiryClicks: 10,
    };

    return {
        default: {
            [SHARE_LINK_TYPE.ANYONE]: defaultValue,
            [SHARE_LINK_TYPE.PASSWORD]: defaultValue,
            [SHARE_LINK_TYPE.DIRECT]: null,
            [SHARE_LINK_TYPE.DOMAIN]: defaultValue,
            [SHARE_LINK_TYPE.RECIPIENTS]: defaultValue,
        },
        maxAllowed: {
            [SHARE_LINK_TYPE.ANYONE]: maxAllowed,
            [SHARE_LINK_TYPE.PASSWORD]: maxAllowed,
            [SHARE_LINK_TYPE.DIRECT]: null,
            [SHARE_LINK_TYPE.DOMAIN]: maxAllowed,
            [SHARE_LINK_TYPE.RECIPIENTS]: maxAllowed,
        },
    };
}

export function getShareLinkPermissionsReadModelMock(): ShareLinkPermissionsReadModel {
    return {
        permissions: {
            linkTypes: {
                [SHARE_LINK_TYPE.ANYONE]: false,
                [SHARE_LINK_TYPE.DIRECT]: true,
                [SHARE_LINK_TYPE.DOMAIN]: true,
                [SHARE_LINK_TYPE.RECIPIENTS]: true,
            },
        },
        defaults: {
            linkTypes: {
                [SHARE_LINK_TYPE.ANYONE]: true,
                [SHARE_LINK_TYPE.DIRECT]: true,
                [SHARE_LINK_TYPE.DOMAIN]: true,
                [SHARE_LINK_TYPE.RECIPIENTS]: true,
            },
            default: SHARE_LINK_TYPE.ANYONE,
        },
        expiration: getShareLinkExpirationMock(),
    };
}
