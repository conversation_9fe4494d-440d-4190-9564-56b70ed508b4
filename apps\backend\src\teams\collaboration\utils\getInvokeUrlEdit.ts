import { <PERSON>no<PERSON>ogger } from 'nestjs-pino';
import { InternalServerErrorException } from '@nestjs/common';
import { ERROR_CODE_TYPE } from 'ms-metaos-types/enums';
import { egCoeditIntegrationsMap, EGAppsProviderIntegration } from '../../common/services/egnyte/egnyte.types';
import { getEditorByFileExtension } from './getEditorByFileExtension';
import { BackendException } from '../../common/utils/BackendException';

const notSupportedIntegrationsInNestedWindow = [
    'msWordSharepointOnlineEdit',
    'msExcelSharepointOnlineEdit',
    'msPowerPointSharepointOnlineEdit',
];

export function getInvokeUrlEdit({
    appDefinition,
    action,
    fileName,
    target,
    logger,
}: {
    appDefinition: EGAppsProviderIntegration;
    action: 'edit' | 'editdesktop';
    fileName: string;
    target: 'nested' | 'tab' | 'desktop';
    logger: PinoLogger;
}): string {
    const extension = fileName.split('.').pop().toLowerCase();
    const editor = getEditorByFileExtension(extension);

    if (!editor) {
        logger.error(`[getInvokeUrlEdit] editor not found for fileName "${fileName}"`);
        throw new InternalServerErrorException('Could not get invoke url for editing');
    }

    const integrationName: string = egCoeditIntegrationsMap[appDefinition.appId][editor][action];

    if (!appDefinition.integrations[integrationName]) {
        logger.error(`[getInvokeUrlEdit] integration "${integrationName}" not found in "${appDefinition.appId}"`);
        throw new BackendException({
            message: 'Coedit app not enabled',
            status: 424,
            code: ERROR_CODE_TYPE.NOT_FOUND_ERROR,
        });
    }

    // disable handling SPE integration in nested window
    if (target === 'nested' && notSupportedIntegrationsInNestedWindow.includes(integrationName)) {
        logger.warn(`[getInvokeUrlEdit] integration "${integrationName}" not supported in nested window`);
        throw new BackendException({
            message: 'Coedit app not supported in nested window',
            status: 409,
            code: ERROR_CODE_TYPE.FORBIDDEN,
        });
    }

    if (!appDefinition.integrations[integrationName].extensions?.includes(extension)) {
        logger.error(`[getInvokeUrlEdit] extension "${extension}" not supported by "${integrationName}"`);
        throw new InternalServerErrorException('File extension not supported');
    }

    const invokeUrl = appDefinition.integrations[integrationName].serviceUrl;

    logger.debug('[getInvokeUrlEdit] received invoke URL from app definition', {
        appId: appDefinition.appId,
        integrationName,
        invokeUrl,
    });

    return invokeUrl;
}
