import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AuthModule } from '../auth/auth.module';
import { ShareLinkController } from './controllers/share-link.controller';
import { ShareLinkService } from './services/share-link.service';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';
import { DomainEnvMetadata } from '../common/utils/domain-env-metadata';

@Module({
    imports: [AuthModule, EgnyteClientServiceModule, HttpModule],
    controllers: [ShareLinkController],
    providers: [ShareLinkService, DomainEnvMetadata],
})
export class ShareLinkModule {}
