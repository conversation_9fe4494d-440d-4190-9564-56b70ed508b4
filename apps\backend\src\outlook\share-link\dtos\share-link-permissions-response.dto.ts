import { ApiProperty } from '@nestjs/swagger';
import { ShareLinkPermissionsResultNestedDto } from './share-link-permissions-result-nested.dto';
import { ShareLinkPermissionsDefaultsNestedDto } from './share-link-permissions-defaults-nested.dto';
import { ShareLinkPermissionsExpirationNestedDto } from './share-link-permissions-expiration-nested.dto';
import { ShareLinkPermissionsReadModel } from '../models/share-link-permissions/share-link-permissions-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class ShareLinkPermissionsResponseDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkPermissionsReadModel): ShareLinkPermissionsResponseDto {
        return {
            permissions: ShareLinkPermissionsResultNestedDto.fromResponse(data),
            defaults: ShareLinkPermissionsDefaultsNestedDto.fromResponse(data),
            expiration: ShareLinkPermissionsExpirationNestedDto.fromResponse(data),
        };
    }

    @ApiProperty({ type: ShareLinkPermissionsResultNestedDto })
    public permissions: ShareLinkPermissionsResultNestedDto;

    @ApiProperty({ type: ShareLinkPermissionsDefaultsNestedDto })
    public defaults: ShareLinkPermissionsDefaultsNestedDto;

    @ApiProperty({ type: ShareLinkPermissionsExpirationNestedDto })
    public expiration: ShareLinkPermissionsExpirationNestedDto;
}
