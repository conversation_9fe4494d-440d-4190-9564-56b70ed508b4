import { FilePickerItemReadModel } from '../file-picker-item/file-picker-item-read.model';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';
import {
    IEgnyteSdkPromiseRequestBookmarks,
    IEgnyteSdkPromiseRequestRecentFiles,
    IEgnyteSearchResult,
} from '../../../common/interfaces/egnyte-provider.interface';
import { PaginationModel } from '../../../common/models/pagination/pagination.model';

export class FilePickerPageReadModel extends PaginationModel<FilePickerItemReadModel> {
    @ValidateArgs()
    public static fromBookmarksResponse(data: IEgnyteSdkPromiseRequestBookmarks): FilePickerPageReadModel {
        return {
            offset: data.offset,
            count: data.count,
            items: data.bookmarks.map((bookmark) => FilePickerItemReadModel.fromItemWithPath(bookmark)),
        };
    }

    @ValidateArgs()
    public static fromSearchResultsResponse(data: IEgnyteSearchResult): FilePickerPageReadModel {
        return {
            offset: data.offset,
            count: data.count,
            items: data.results.map((item) => FilePickerItemReadModel.fromSDKItem(item)),
            total: data.total_count,
        };
    }

    @ValidateArgs()
    public static fromRecentsResponse(data: IEgnyteSdkPromiseRequestRecentFiles): FilePickerPageReadModel {
        return {
            offset: data.offset,
            count: data.count,
            items: data.recentFiles.map((item) => FilePickerItemReadModel.fromSDKItem(item)),
        };
    }
}
