import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { SHARE_LINK_TYPE } from '../../share-link/enums/share-link-type.enum';
import { Mappers } from '../../common/utils/mappers';
import { UploadLinkRequestDto } from '../dtos/upload-link-request.dto';
import { UploadLinkCreateExpirationModel } from './upload-link-create-expiration.model';
import { UploadLinkCreateItemModel } from './upload-link-create-item.model';

export class UploadLinkCreateModel {
    @ValidateArgs()
    public static fromDto(data: UploadLinkRequestDto): UploadLinkCreateModel {
        return {
            expiration: UploadLinkCreateExpirationModel.fromDto(data.expiration),
            item: UploadLinkCreateItemModel.fromDto(data.items.pop()),
            shareType: Mappers.mapRawToModelLinkType(data.shareType),
        };
    }

    public item: UploadLinkCreateItemModel;

    public shareType: SHARE_LINK_TYPE;

    public expiration: UploadLinkCreateExpirationModel;
}
