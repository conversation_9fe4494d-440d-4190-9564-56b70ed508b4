import { Injectable } from '@nestjs/common';
import * as Reporting from '@integrations/pint-reporting';
import { ConfigService } from '@nestjs/config';
import { RedisClientService } from 'ms-metaos-modules';

@Injectable()
export class ReportingService {
    constructor(
        private readonly redisClientService: RedisClientService,
        private readonly configService: ConfigService
    ) {
        Reporting.initialize({
            appId: this.configService.get('reporting.appId'),
            eventsQueueName: this.configService.get('eventsQueueName'),
            sender: {
                send: (data: unknown, opts: { eventsQueueName?: string }) =>
                    this.redisClientService.publish(data, opts),
            },
        });
    }
}
