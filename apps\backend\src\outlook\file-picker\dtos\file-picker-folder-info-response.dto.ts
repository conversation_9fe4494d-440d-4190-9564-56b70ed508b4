import { ApiProperty } from '@nestjs/swagger';
import { FilePickerFolderItemNestedDto } from './file-picker-folder-item-nested.dto';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { FilePickerPageReadModel } from '../models/file-picker-page/file-picker-page-read.model';
import { FilePickerItemReadModel } from '../models/file-picker-item/file-picker-item-read.model';

export class FilePickerFolderInfoResponseDto {
    @ValidateArgs()
    public static fromBookmarksResponse(data: FilePickerPageReadModel): FilePickerFolderInfoResponseDto {
        return {
            folderId: 'bookmarks',
            path: 'bookmarks',
            items: (data.items ?? []).map((model) => FilePickerFolderItemNestedDto.fromResponse(model)),
        };
    }

    @ValidateArgs()
    public static fromSearchResultsResponse(data: FilePickerPageReadModel): FilePickerFolderInfoResponseDto {
        return {
            folderId: 'searchResults',
            path: 'searchResults',
            items: (data.items ?? []).map((model) => FilePickerFolderItemNestedDto.fromResponse(model)),
        };
    }

    @ValidateArgs()
    public static fromRecentsResponse(data: FilePickerPageReadModel): FilePickerFolderInfoResponseDto {
        return {
            folderId: 'recents',
            path: 'recents',
            items: (data.items ?? []).map((model) => FilePickerFolderItemNestedDto.fromResponse(model)),
        };
    }

    @ValidateArgs()
    public static fromFolderInfoResponse(data: FilePickerItemReadModel): FilePickerFolderInfoResponseDto {
        return {
            folderId: data.folderId,
            path: data.path,
            items: (data.items ?? []).map((model) => FilePickerFolderItemNestedDto.fromResponse(model)),
        };
    }

    @ApiProperty()
    public folderId: string;

    @ApiProperty()
    public path: string;

    @ApiProperty({ type: () => FilePickerFolderItemNestedDto })
    public items: FilePickerFolderItemNestedDto[];
}
