import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiCreatedResponse, ApiHeader, ApiTags } from '@nestjs/swagger';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { UploadLinkService } from '../services/upload-link.service';
import { UploadLinkRequestDto } from '../dtos/upload-link-request.dto';
import { UploadLinkResponseDto } from '../dtos/upload-link-response.dto';
import { ReportingActions } from '../../common/services/reporting/reporting.enums';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { Auth } from '../../common/decorators/auth.decorator';
import { UploadLinkCreateModel } from '../models/upload-link-create.model';

@ApiTags('UploadLinkController')
@ApiHeader({
    name: 'Authorization',
    description: 'Contains JWT token',
})
@Controller('outlook/upload-link')
@UseGuards(JwtGuard)
export class UploadLinkController {
    constructor(
        private readonly reportingService: ReportingService,
        private readonly uploadLinkService: UploadLinkService
    ) {}

    @ApiCreatedResponse({
        type: UploadLinkResponseDto,
    })
    @Post()
    public async createUploadLink(
        @Auth() auth: AuthReadModel,
        @Body() uploadLinkRequestDto: UploadLinkRequestDto
    ): Promise<UploadLinkResponseDto[]> {
        const uploadLinkCreateModel = UploadLinkCreateModel.fromDto(uploadLinkRequestDto);

        const result = await this.uploadLinkService.createUploadLink(uploadLinkCreateModel);

        this.reportingService.processEvent({
            action: ReportingActions.SHARE_LINK,
            domain: auth.egnyte.domain,
            username: auth.egnyte.username,
            userId: auth.egnyte.id,
            fileId: uploadLinkCreateModel.item.id,
            tags: `${ReportingActions.SHARE_LINK}-${uploadLinkCreateModel.shareType},${result.type}`,
            statusCode: 201,
        });

        return [result];
    }
}
