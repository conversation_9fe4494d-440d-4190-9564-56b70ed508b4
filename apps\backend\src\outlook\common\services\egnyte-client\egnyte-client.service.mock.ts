import { getAuthReadModelMock } from 'ms-metaos-database/outlook';
import { IPublic } from '../../utils/public.interface';
import { EgnyteClientService } from './egnyte-client.service';
import {
    EGNYTE_LINK_TYPE,
    EGNYTE_PERMISSION,
    EGNYTE_PUBLIC_LINKS_ALLOWED_FOR,
    IEgnyteInstance,
    IEgnyteSdkItem,
    IEgnyteSdkMetadataNamespaceRead,
    IEgnyteSdkResult,
    IEgnyteSearchResult,
    IEgnyteUserInfo,
} from '../../interfaces/egnyte-provider.interface';
import { UNIT_TEST_VARS } from '../../utils/unit-test-vars';
import { Mappers } from '../../utils/mappers';
import { getFilePickerItemReadModelMock } from '../../../file-picker/models/file-picker-item/file-picker-item-read.mock';
import { getFilePickerPageReadModelMock } from '../../../file-picker/models/file-picker-page/file-picker-page-read.mock';
import { getUserDetailsModelMock } from '../../models/user-details/user-details.mock';
import { getShareLinkUsersReadModelMock } from '../../../share-link/models/share-link-recipients/share-link-users-read.mock';
import { getShareLinkPermissionsReadModelMock } from '../../../share-link/models/share-link-permissions/share-link-permissions-read.mock';

export function getEgnyteSdkItemMock({ itemId = 'testItemId', isFolder = true } = {}): IEgnyteSdkItem {
    return {
        id: itemId,
        name: itemId,
        path: 'testFolderPath',
        is_folder: isFolder,
        parent_id: 'testParentId',
        ...(!isFolder && {
            group_id: itemId,
            checksum: 'testChecksum',
            entry_id: 'testEntryId',
            last_modified: '',
            locked: false,
            num_versions: 1,
            size: 1000,
            uploaded: 1000,
            uploaded_by: 'testUploadedBy',
        }),
        ...(isFolder && {
            folder_id: itemId,
            lastModified: 1000,
        }),
    };
}

export function getEgnyteSdkResultMock({
    withoutListContents = false,
    withAllowedLinkTypes = false,
    withIncludedPerm = false,
} = {}): IEgnyteSdkResult {
    return {
        allow_links: true,
        count: 1,
        is_folder: true,
        lastModified: 1000,
        offset: 0,
        public_links: EGNYTE_PUBLIC_LINKS_ALLOWED_FOR.FILES_FOLDERS,
        restrict_move_delete: false,
        total_count: 4,
        uploaded: 1000,
        name: 'testName',
        folder_id: 'testFolderId',
        parent_id: 'testParentId',
        path: 'testPath',
        ...(!withoutListContents && {
            folders: [
                getEgnyteSdkItemMock({ itemId: 'testFolderItemId1' }),
                getEgnyteSdkItemMock({ itemId: 'testFolderItemId2' }),
            ],
        }),
        ...(!withoutListContents && {
            files: [
                getEgnyteSdkItemMock({
                    itemId: 'testFileItemId1',
                    isFolder: false,
                }),
                getEgnyteSdkItemMock({
                    itemId: 'testFileItemId2',
                    isFolder: false,
                }),
            ],
        }),
        ...(withAllowedLinkTypes && {
            allow_upload_links: true,
            allowed_file_link_types: [EGNYTE_LINK_TYPE.ANYONE, EGNYTE_LINK_TYPE.DOMAIN, EGNYTE_LINK_TYPE.RECIPIENTS],
            allowed_folder_link_types: [EGNYTE_LINK_TYPE.DOMAIN],
        }),
        ...(withIncludedPerm && {
            permission: EGNYTE_PERMISSION.OWNER,
        }),
    };
}

export function getEgnyteUserInfoMock(): IEgnyteUserInfo {
    return {
        email: UNIT_TEST_VARS.egEmail,
        first_name: UNIT_TEST_VARS.egFirstName,
        id: UNIT_TEST_VARS.egId,
        last_name: UNIT_TEST_VARS.egLastName,
        user_type: Mappers.mapModelToRawUserType(UNIT_TEST_VARS.egUserType),
        username: UNIT_TEST_VARS.egUsername,
    };
}

export function getEgnyteSdkSearchResultMock(): IEgnyteSearchResult {
    return {
        results: [
            getEgnyteSdkItemMock({
                isFolder: false,
                itemId: 'testFoundId1',
            }),
            getEgnyteSdkItemMock({
                isFolder: false,
                itemId: 'testFoundId2',
            }),
        ],
        offset: 0,
        count: 2,
        hasMore: false,
        total_count: 2,
    };
}

function getEgnyteClientInstanceMock(): IEgnyteInstance {
    const authMock = {
        getUserInfo: jest.fn().mockResolvedValue(getEgnyteUserInfoMock()),
    };

    const manualMock = {
        promiseRequest: jest.fn(),
    };

    const storageMock = {
        folderId: jest.fn().mockReturnValue({
            get: jest.fn().mockResolvedValue(getEgnyteSdkResultMock()),
        }),
        path: jest.fn().mockReturnValue({
            get: jest.fn().mockResolvedValue(getEgnyteSdkResultMock()),
            storeFile: jest.fn().mockResolvedValue(getEgnyteSdkItemMock({ isFolder: false })),
        }),
    };

    const searchMock = {
        query: jest.fn().mockResolvedValue(getEgnyteSdkSearchResultMock()),
    };

    const withRequestId = (functions) => ({ requestId: jest.fn().mockReturnValue(functions), ...functions });

    return {
        domain: UNIT_TEST_VARS.egDomain,
        API: {
            auth: authMock,
            manual: manualMock,
            storage: withRequestId(storageMock),
            search: searchMock,
        },
    };
}

export const testUploadLink = {
    url: 'https://pint.qa-egnyte.com/ul/fRdePmNutH',
    name: 'Test-Folder',
    type: 'uploadFolder',
};
const getUploadLinkResponseMock = () => ({
    links: [testUploadLink],
    path: '/Shared/Test-Folder',
    type: 'upload',
});

export function getEgnyteClientServiceMock(): IPublic<EgnyteClientService> {
    return {
        deleteFile: jest.fn(),
        addMetadataNamespace: jest.fn(),
        removeMetadataNamespace: jest.fn(),
        getMetadataNamespaces: jest
            .fn()
            .mockResolvedValue(UNIT_TEST_VARS.egNamespaces as IEgnyteSdkMetadataNamespaceRead[]),
        addMetadataKeysValues: jest.fn(),
        getFolderDetailsById: jest.fn().mockResolvedValue(
            getFilePickerItemReadModelMock({
                isFolder: true,
                withChildren: true,
            })
        ),
        getFolderDetailsByPath: jest.fn().mockResolvedValue(
            getFilePickerItemReadModelMock({
                isFolder: true,
                withChildren: true,
            })
        ),
        getPermissionsForItemId: jest.fn().mockResolvedValue(getShareLinkPermissionsReadModelMock()),
        getBookmarkedItems: jest.fn().mockResolvedValue(
            getFilePickerPageReadModelMock({
                itemsType: 'folders',
            })
        ),
        getRecentItems: jest.fn().mockResolvedValue(getFilePickerPageReadModelMock({ itemsType: 'files' })),
        getUserInfo: jest.fn().mockResolvedValue(getUserDetailsModelMock()),
        searchForItems: jest.fn().mockResolvedValue(getFilePickerPageReadModelMock()),
        searchForUsers: jest.fn().mockResolvedValue(getShareLinkUsersReadModelMock()),
        uploadFile: jest.fn().mockResolvedValue(getFilePickerItemReadModelMock()),
        getInstance: jest.fn().mockResolvedValue(getEgnyteClientInstanceMock()),
        getUserCredentials: jest.fn().mockResolvedValue(getAuthReadModelMock()),
        getFileDefaultAttributes: jest.fn(),
        getFileSharingRestrictions: jest.fn(),
        getPermissionsForFile: jest.fn().mockResolvedValue(getShareLinkPermissionsReadModelMock()),
        getPermissionsForFolder: jest.fn().mockResolvedValue(getShareLinkPermissionsReadModelMock()),
        createUploadLink: jest.fn().mockResolvedValue(getUploadLinkResponseMock()),
    };
}
