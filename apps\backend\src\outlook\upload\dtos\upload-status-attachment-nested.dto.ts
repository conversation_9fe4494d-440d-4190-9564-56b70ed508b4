import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UploadReadModel } from 'ms-metaos-database/outlook';
import { UPLOAD_STATUS } from '../enums/upload-status.enum';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class UploadStatusAttachmentNestedDto {
    @ValidateArgs()
    public static fromResponse(data: UploadReadModel): UploadStatusAttachmentNestedDto {
        return {
            egFileName: data.egFileName,
            egPath: data.egPath,
            msAttachmentId: data.msAttachmentId,
            msFileName: data.msFileName,
            status: data.status,
            ...(data.egGroupId && { egGroupId: data.egGroupId }),
        };
    }

    @ApiProperty({ type: String, enum: UPLOAD_STATUS })
    public readonly status: UPLOAD_STATUS;

    @ApiProperty()
    public readonly msAttachmentId: string;

    @ApiProperty()
    public readonly egFileName: string;

    @ApiProperty()
    public readonly egPath: string;

    @ApiPropertyOptional()
    public readonly egGroupId?: string;

    @ApiProperty()
    public readonly msFileName: string;
}
