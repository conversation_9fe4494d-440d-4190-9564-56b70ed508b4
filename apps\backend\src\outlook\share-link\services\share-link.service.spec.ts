import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import { ShareLinkService } from './share-link.service';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { getEgnyteClientServiceMock } from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { DomainEnvMetadata } from '../../common/utils/domain-env-metadata';
import { getDomainEnvMetadataMock } from '../../common/utils/domain-env-metadata.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getShareLinkUsersReadModelMock } from '../models/share-link-recipients/share-link-users-read.mock';

describe('ShareLinkService', () => {
    const argsMock = {
        recipientQueryString: 'mcz',
    };
    let testingModule: TestingModule;

    let egnyteClientService: EgnyteClientService;
    let shareLinkService: ShareLinkService;
    beforeEach(async () => {
        jest.resetAllMocks();
        testingModule = await Test.createTestingModule({
            providers: [
                { provide: EgnyteClientService, useValue: getEgnyteClientServiceMock() },
                { provide: DomainEnvMetadata, useValue: getDomainEnvMetadataMock() },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                ShareLinkService,
            ],
        }).compile();

        egnyteClientService = testingModule.get<EgnyteClientService>(EgnyteClientService);
        shareLinkService = testingModule.get<ShareLinkService>(ShareLinkService);
    });

    describe('createShareLinkByFileId', () => {
        it.todo('Create createShareLinkByFileId method tests');
    });

    describe('getPermissions', () => {
        it.todo('Create getPermissions method tests');
    });

    describe('searchForRecipients', () => {
        it('should successfully get RecipientsInfo', async () => {
            const expected = getShareLinkUsersReadModelMock();
            const result = await shareLinkService.searchForRecipients(argsMock.recipientQueryString);

            expect(egnyteClientService.searchForUsers).toHaveBeenCalledWith(argsMock.recipientQueryString);
            expect(result).toEqual(expected);
        });
    });
});
