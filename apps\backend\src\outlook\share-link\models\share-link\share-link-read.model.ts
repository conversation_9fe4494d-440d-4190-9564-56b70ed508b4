import { ISharePintLinkResponse } from '../../interfaces/share-pint-link-response.interface';
import { SHARE_LINK_ITEM_TYPE } from '../../enums/share-link-item-type.enum';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class ShareLinkReadModel {
    @ValidateArgs()
    public static fromResponse(data: ISharePintLinkResponse, type: SHARE_LINK_ITEM_TYPE): ShareLinkReadModel {
        return {
            url: data.url,
            type,
            name: data.name,
        };
    }

    public url: string;

    public name: string;

    public type: SHARE_LINK_ITEM_TYPE;
}
