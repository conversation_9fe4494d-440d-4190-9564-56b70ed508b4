import { IsOptional, IsString, ValidateNested } from '@nestjs/class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SettingsPatchEgMetadataRequestNestedDto } from './settings-patch-eg-metadata-request-nested.dto';

export class SettingsPatchRequestDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public egUploadFolderId?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @Type(() => SettingsPatchEgMetadataRequestNestedDto)
    @ValidateNested()
    public egMetadata?: SettingsPatchEgMetadataRequestNestedDto;
}
