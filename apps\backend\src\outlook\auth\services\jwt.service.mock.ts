import { JwtService } from '@nestjs/jwt';
import { IPublic } from '../../common/utils/public.interface';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';

export function getJwtServiceMock(): IPublic<JwtService> {
    return {
        decode: jest.fn(),
        sign: jest.fn().mockReturnValue(UNIT_TEST_VARS.accessToken),
        signAsync: jest.fn().mockResolvedValue(UNIT_TEST_VARS.accessToken),
        verify: jest.fn(),
        verifyAsync: jest.fn(),
    };
}
