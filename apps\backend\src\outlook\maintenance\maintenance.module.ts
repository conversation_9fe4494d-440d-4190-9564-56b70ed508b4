import { Modu<PERSON> } from '@nestjs/common';
import { LoggerModule } from 'nestjs-pino';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { DbConnection } from 'ms-metaos-types/types';
import { DatabaseAuthModule, DatabaseSettingsModule } from 'ms-metaos-database/outlook';
import { MaintenanceController } from './controllers/maintenance.controller';
import { MaintenanceService } from './services/maintenance.service';
import { configConfigModule, configLoggerModule, configMongooseModule } from '../common/config/module-config';

@Module({
    imports: [
        ConfigModule.forRoot(configConfigModule()),
        MongooseModule.forRootAsync(configMongooseModule(DbConnection.Outlook)),
        LoggerModule.forRootAsync(configLoggerModule()),
        DatabaseAuthModule,
        DatabaseSettingsModule,
        JwtModule.register({}),
    ],
    controllers: [MaintenanceController],
    providers: [MaintenanceService],
})
export class MaintenanceModule {}
