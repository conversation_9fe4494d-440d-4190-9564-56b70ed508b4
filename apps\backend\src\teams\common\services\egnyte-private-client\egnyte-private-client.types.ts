export interface IShareLinkPermissionsResult {
    [key: string]: {
        [type in SHARE_LINK_ACCESS_TYPE]: SHARE_LINK_AVAILABILITY;
    };
}

export interface IShareLinkCreateProps {
    lastEntryId: string;
    sourceId: string;
    shareLinkType: SHARE_LINK_ACCESS_TYPE;
}

export interface IShareLink {
    link_id: string;
    file_name: string;
    folder_path: string;
    preview_url: string;
    share_date: Date;
    is_active: boolean;
    open_in_egnyte_url?: string;
    download_url?: string;
}

export enum SHARE_LINK_ACCESS_TYPE {
    ANYONE = 'ANYONE',
    CHANNEL = 'CHANNEL',
    EMPLOYEES = 'EMPLOYEES',
    PRIVILEGED = 'PRIVILEGED',
}

export enum SHARE_LINK_AVAILABILITY {
    AVAILABLE = 'AVAILABLE',
    BLOCKED_BY_WORKGROUP = 'BLOCKED_BY_WORKGROUP',
    BLOCKED_BY_FOLDER = 'BLOCKED_BY_FOLDER',
    BLOCKED_BY_FILE = 'BLOCKED_BY_FILE',
}

export interface IShareLinkListProps {
    sourceId: string;
    limit?: number;
    offset?: number;
    sortBy?: 'FILE_NAME' | 'FOLDER_PATH' | 'SHARE_DATE';
    sortDirection?: 'ASC' | 'DESC';
}

export interface IShareLinkListResult {
    total: number;
    contents: IShareLink[];
}

export type Env = {
    workgroup: {
        name: string;
    };
    analytics: {
        dcName: string;
    };
};

export interface ISignature {
    subject: {
        url: string;
        accessUrl: string | null;
        expiration: number;
    };
    signature: string;
}
