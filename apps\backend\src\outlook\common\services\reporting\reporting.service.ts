import { Injectable } from '@nestjs/common';
import * as Reporting from '@integrations/pint-reporting';
import { NextFunction, Response } from 'express';
import { utils } from '@integrations/pint-common';
import { IMonitoringMiddleware } from '../../monitoring/monitoring.interface';
import { IReportMetadata } from './reporting.interface';
import { IAuthRequest } from '../../interfaces/auth-data-request.interface';

@Injectable()
export class ReportingService implements IMonitoringMiddleware {
    public static getEventFields(req: IAuthRequest): IReportMetadata {
        return {
            userId: req?.user?.egnyte?.id ?? 'n/a',
            domain: req?.user?.egnyte?.domain ?? 'n/a',
            username: req?.user?.egnyte?.username ?? 'n/a',
            userAgent: `${req?.headers?.['user-agent']} ${req?.headers?.['x-outlook-platform'] ?? 'n/a'} ${
                req?.headers?.['x-outlook-version'] ?? 'n/a'
            }`,
            ip: req?.headers?.['x-forwarded-for'],
        };
    }

    public static calculateOperationTime(start: number): number {
        return Math.round((Date.now() - start) / 1000);
    }

    public getMiddleware(): (req: IAuthRequest, res: Response, next: NextFunction) => void {
        return Reporting.getMiddleware({
            includePath: true,
            excludePath: (req: IAuthRequest) => this.excludePath(req),
            getPath: (req: IAuthRequest) => utils.normalizePath(req, { valueMasks: [/(\w|\d|-)=$/] }),
            getEventFields: (req: IAuthRequest) => ReportingService.getEventFields(req),
        });
    }

    public processEvent(data: Record<string, unknown>): void {
        Reporting.processEvent(data);
    }

    private excludePath(req: IAuthRequest) {
        const pathsToExclude = ['/static', '/metrics', '/health'];

        return !!pathsToExclude.find((excludedRoute) => req.url.match(excludedRoute));
    }
}
