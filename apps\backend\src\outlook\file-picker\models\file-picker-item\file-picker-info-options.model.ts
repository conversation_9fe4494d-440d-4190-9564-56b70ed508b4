import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';
import { FilePickerFolderInfoQueryDto } from '../../dtos/file-picker-folder-info-query.dto';

export class FilePickerInfoOptionsModel {
    @ValidateArgs()
    public static fromDto(data: FilePickerFolderInfoQueryDto): FilePickerInfoOptionsModel {
        return {
            withChildren: data.withChildren,
        };
    }

    public withChildren: boolean;
}
