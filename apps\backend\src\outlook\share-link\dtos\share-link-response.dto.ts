import { ApiProperty } from '@nestjs/swagger';
import { ShareLinkReadModel } from '../models/share-link/share-link-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { SHARE_LINK_ITEM_TYPE } from '../enums/share-link-item-type.enum';

export class ShareLinkResponseDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkReadModel): ShareLinkResponseDto {
        return {
            url: data.url,
            name: data.name,
            type: data.type,
        };
    }

    @ApiProperty()
    public url: string;

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public type: SHARE_LINK_ITEM_TYPE;
}
