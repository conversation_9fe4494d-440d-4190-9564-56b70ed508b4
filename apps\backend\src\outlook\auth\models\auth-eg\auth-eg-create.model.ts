import { AuthCodeQueryDto } from '../../dtos/auth-code-query.dto';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class AuthEGCreateModel {
    @ValidateArgs()
    public static fromDto(data: AuthCodeQueryDto, redirectUri: string): AuthEGCreateModel {
        return {
            userId: decodeURIComponent(data.state),
            code: data.code,
            redirectUri,
            ...(data.domain && { domain: data.domain }),
            ...(data.error && { error: data.error }),
        };
    }

    public userId: string;

    public code: string;

    public redirectUri: string;

    public domain?: string;

    public error?: string;
}
