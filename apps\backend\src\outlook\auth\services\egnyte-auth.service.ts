import { HttpService } from '@nestjs/axios';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PinoLogger } from 'nestjs-pino';
import { inspect } from 'util';
import { firstValueFrom } from 'rxjs';
import { DomainEnvMetadata } from '../../common/utils/domain-env-metadata';
import { Helpers } from '../../common/utils/helpers';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { IEgnyteUserInfo } from '../../common/interfaces/egnyte-provider.interface';
import { AuthEGCreateModel } from '../models/auth-eg/auth-eg-create.model';
import { AuthEGReadModel } from '../models/auth-eg/auth-eg-read.model';
import { UserDetailsModel } from '../../common/models/user-details/user-details.model';

@Injectable()
export class EgnyteAuthService {
    private clientId;

    private clientSecret;

    private scope;

    private domainBaseUrl;

    private authBaseUrl;

    constructor(
        configService: ConfigService,
        private readonly domainEnvMetadata: DomainEnvMetadata,
        private readonly httpService: HttpService,
        private readonly egnyteClientService: EgnyteClientService,
        private readonly logger: PinoLogger
    ) {
        logger.setContext(EgnyteAuthService.name);

        this.clientId = configService.get('definitionApiKey');
        this.clientSecret = configService.get('masheryAppSecret');
        this.scope = configService.get('tokenScope');
        this.domainBaseUrl = configService.get('egDomainBaseUrl');
        this.authBaseUrl = configService.get('egAuthBaseUrl');
    }

    getAuthUrl(userId: string, redirectUri: string, domain?: string): string {
        const additionalFields: { scope?: string; domain?: string } = {};

        if (this.scope) {
            additionalFields.scope = this.scope;
        }

        if (!userId) {
            throw new Error('Session id is missing in request');
        }

        if (domain) {
            additionalFields.domain = domain;
        }

        const queryString = Helpers.buildQueryString({
            client_id: this.clientId,
            redirect_uri: redirectUri,
            state: encodeURIComponent(userId),
            include_domain: 'true',
            response_type: 'code',
            ...additionalFields,
        });

        return `${this.authBaseUrl}?${queryString}`;
    }

    async handleAuthCode(authEGCreate: AuthEGCreateModel): Promise<AuthEGReadModel> {
        const { error, code, userId: state, domain: domainName, redirectUri } = authEGCreate;

        if (error) {
            this.logger.error(`Handle auth code failed. Error: ${inspect(error)}`);
            throw new ForbiddenException('Access was denied. Please try again');
        }

        if (!code) {
            this.logger.error('Handle auth code failed. Code not provided');
            throw new BadRequestException('Code not provided!');
        }

        if (!domainName) {
            this.logger.error('Handle auth code failed. Domain not provided');
            throw new BadRequestException('Domain not provided!');
        }
        let domain;
        try {
            domain = await this.domainEnvMetadata.getValidDomainRecordFromInput(domainName, this.domainBaseUrl);
        } catch (err) {
            this.logger.error(`Handle domain failed. Error: ${err.message ?? 'Unknown'}`);
            throw new BadRequestException('Not a valid Egnyte domain');
        }

        this.logger.assign({ domain: Helpers.sanitizeDomain(domain) });

        const additionalFields: { scope?: string } = {};

        if (this.scope) {
            additionalFields.scope = this.scope;
        }

        const queryString = Helpers.buildQueryString({
            client_id: this.clientId,
            client_secret: this.clientSecret,
            code,
            grant_type: 'authorization_code',
            redirect_uri: `${redirectUri}?domain=${domainName}`,
            ...additionalFields,
        });

        try {
            const response = await firstValueFrom(
                this.httpService.post<{ access_token: string }>(`${domain}/puboauth/token`, queryString)
            );
            this.logger.info(`Auth - token acquired for domain: ${domain}`);

            const userInfo = await firstValueFrom(
                this.httpService.get<IEgnyteUserInfo>(`${domain}/pubapi/v1/userinfo`, {
                    headers: { Authorization: `Bearer ${response.data.access_token}` },
                })
            );

            return AuthEGReadModel.fromResponse(state, domain, response.data, userInfo.data);
        } catch (err) {
            this.logger.error(`Handle auth request failed. Error: ${err.message ?? 'Unknown'}`);
            throw new BadRequestException('Handle auth request failed');
        }
    }

    async getEgUserInfo(): Promise<UserDetailsModel> {
        return this.egnyteClientService.getUserInfo();
    }
}
