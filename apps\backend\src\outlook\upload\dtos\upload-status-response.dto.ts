import { ApiProperty } from '@nestjs/swagger';
import { UploadReadModel } from 'ms-metaos-database/outlook';
import { UploadStatusAttachmentNestedDto } from './upload-status-attachment-nested.dto';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class UploadStatusResponseDto {
    @ValidateArgs()
    public static fromResponse(data: UploadReadModel[], msMessageId: string): UploadStatusResponseDto {
        return {
            msMessageId,
            attachments: data.map((attachment) => UploadStatusAttachmentNestedDto.fromResponse(attachment)),
        };
    }

    @ApiProperty()
    public readonly msMessageId: string;

    @ApiProperty({ type: UploadStatusAttachmentNestedDto, isArray: true })
    public readonly attachments: UploadStatusAttachmentNestedDto[];
}
