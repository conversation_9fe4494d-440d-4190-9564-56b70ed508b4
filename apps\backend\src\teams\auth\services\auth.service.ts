import {
    BadRequestException,
    ForbiddenException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import {
    AdminAuthReadModel,
    AuthSetupCreateModel,
    AuthSetupReadModel,
    AuthReadModel,
    AdminAuthRepository,
    AuthSetupRepository,
    AuthRepository,
} from 'ms-metaos-database/teams';
import { ERROR_CODE_TYPE } from 'ms-metaos-types/enums';
import { MicrosoftAuthClientService } from 'ms-metaos-modules';
import { IdentityReadModel } from '../../common/models/identity-read.model';
import { EgnytePrivateClientService } from '../../common/services/egnyte-private-client/egnyte-private-client.service';
import { EgnyteService } from '../../common/services/egnyte/egnyte.service';
import { EgnyteUserReadModel } from '../../common/models/egnyte-user.model';
import { EGGetEnvResponse } from '../../common/services/egnyte/egnyte.types';
import { sanitizeDomain } from '../../common/utils/sanitize';
import { BackendException } from '../../common/utils/BackendException';
import { ExchangeAdminTokenResponseDto } from '../dtos/responses/exchange-admin-token-response.dto';
import { LoginQueryDto } from '../dtos/queries/login-query.dto';

@Injectable()
export class AuthService {
    constructor(
        private configService: ConfigService,
        private egnyteService: EgnyteService,
        private egPrivateClientService: EgnytePrivateClientService,
        private msAuthClientService: MicrosoftAuthClientService,
        private authRepository: AuthRepository,
        private authSetupRepository: AuthSetupRepository,
        private adminAuthRepository: AdminAuthRepository,
        private jwtService: JwtService,
        private logger: PinoLogger
    ) {}

    public async getAdminAuthByTenantId(tenantId: string): Promise<AdminAuthReadModel> {
        const adminAuth = await this.adminAuthRepository.getByTenantId(tenantId);

        if (!adminAuth) {
            this.logger.info(`[Auth/start] adminAuth not found, tenantId ${tenantId}`);
            throw new BadRequestException('AdminSetup not finished');
        }

        return adminAuth;
    }

    public async findAdminAuthByTenantId(tenantId: string): Promise<AdminAuthReadModel> {
        const adminAuth = await this.adminAuthRepository.getByTenantId(tenantId);

        return adminAuth;
    }

    public async getAuthSetupById(authSetupId: string): Promise<AuthSetupReadModel> {
        const authSetup: AuthSetupReadModel | null = await this.authSetupRepository.getById(authSetupId);

        if (!authSetup) {
            this.logger.error(`[AuthService/getAuthSetupById] AuthSetup not found, id ${authSetupId}`);
            throw new NotFoundException('AuthSetup not found');
        }

        return authSetup;
    }

    public async addAuthSetup(authSetupCreateModel: AuthSetupCreateModel): Promise<AuthSetupReadModel> {
        return this.authSetupRepository.addAuthSetup(authSetupCreateModel);
    }

    public async exchangeIdentityToken({
        authorizationHeader,
        authSetup,
    }: {
        authorizationHeader: `Bearer ${string}`;
        authSetup: AuthSetupReadModel;
    }): Promise<AuthSetupReadModel> {
        try {
            const identityToken = authorizationHeader.replace('Bearer ', '');

            const tokens = await this.msAuthClientService.exchangeIdentityTokenForAccessToken(
                authSetup.msTenantId,
                identityToken,
                this.configService.get('msScopes')
            );

            const decodedTokenPayload = this.jwtService.decode(tokens.access_token) as { exp: number };

            const updatedAuthSetup = await this.authSetupRepository.updateAuthSetup(authSetup.id, {
                msAccessToken: tokens.access_token,
                msRefreshToken: tokens.refresh_token,
                msTokenExpiration: decodedTokenPayload.exp * 1000,
            });

            this.logger.info(
                {
                    setupId: updatedAuthSetup.id,
                    msUserId: updatedAuthSetup.msUserId,
                },
                '[AuthService/exchangeIdentityToken] saved setup for user with ms tokens'
            );

            return updatedAuthSetup;
        } catch (err) {
            this.logger.warn(err, '[AuthNext/start] Attempt to exchange identity token to accessToken failed');

            return authSetup;
        }
    }

    public async exchangeAdminToken({ user }: { user: AuthReadModel }): Promise<ExchangeAdminTokenResponseDto> {
        try {
            const adminAuth = await this.getAdminAuthByTenantId(user.msTenantId);

            if (!adminAuth) {
                this.logger.info('[Auth/exchangeAdminToken] AdminAuth not found');

                throw new BackendException({
                    message: 'AdminAuth not found',
                    status: HttpStatus.NOT_FOUND,
                    code: ERROR_CODE_TYPE.NOT_FOUND_ERROR,
                });
            }

            const isMainAdmin = await this.egnyteService.isCurrentUserMainAdmin();

            if (!isMainAdmin) {
                this.logger.info('[Auth/exchangeAdminToken] user is not main admin');

                throw new BackendException({
                    message: 'User is not main admin',
                    status: HttpStatus.BAD_REQUEST,
                    code: ERROR_CODE_TYPE.INVALID_REQUEST,
                });
            }

            const payload = {
                adminAuthId: adminAuth.id,
            };

            const adminToken = this.jwtService.sign(payload, {
                secret: this.configService.get('adminJwtTokenSecret'),
                expiresIn: this.configService.get('adminJwtTokenExpiration'),
                algorithm: 'HS512',
            });

            return {
                token: adminToken,
            };
        } catch (error: any) {
            this.logger.error({ error }, '[Auth/exchangeAdminToken] Something went wrong');

            throw new BackendException({
                message: 'Something went wrong',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async handleMsGraphAuthResult(
        id: string,
        msGraphAuthResult: IGetTokenResponse
    ): Promise<AuthSetupReadModel> {
        try {
            const updatedAuthSetup = await this.authSetupRepository.updateAuthSetup(id, {
                msAccessToken: msGraphAuthResult.accessToken,
                msRefreshToken: msGraphAuthResult.refreshToken,
                msTokenExpiration: msGraphAuthResult.expireTime,
            });

            this.logger.info(
                {
                    setupId: updatedAuthSetup.id,
                    msUserId: updatedAuthSetup.msUserId,
                },
                '[AuthService/handleMsGraphAuthResult] saved setup for user with ms tokens'
            );

            return updatedAuthSetup;
        } catch (e) {
            this.logger.error(e, '[AuthService/handleMsGraphAuthResult] saving auth setup error');
            throw new InternalServerErrorException('Saving AuthSetup error');
        }
    }

    public async handleEgAuthResponse(params: {
        redirectUri: string;
        error?: string;
        domain?: string;
        code?: string;
    }): Promise<{ egDomain: string; egAccessToken: string }> {
        const { error, domain: egDomainName, code } = params;

        if (error) {
            this.logger.info(error, '[AuthService/handleEgAuthResponse] initial error');
            throw new BadRequestException(error);
        }

        if (!egDomainName || !code) {
            this.logger.info('[AuthService/handleEgAuthResponse] body params error');
            throw new BadRequestException('Invalid Egnyte auth data');
        }

        const { egDomain } = await this.egnyteService.validateEgDomain(egDomainName);

        const queryString = new URLSearchParams({
            client_id: this.configService.get('definitionApiKey'),
            client_secret: this.configService.get('masheryAppSecret'),
            code,
            grant_type: 'authorization_code',
            redirect_uri: `${params.redirectUri}${params.redirectUri.includes('?') ? '&' : '?'}domain=${egDomainName}`,
            scope: this.configService.get('tokenScope'),
        });

        let egAccessToken;

        try {
            const response = await fetch(`${egDomain}/puboauth/token`, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                method: 'post',
                body: queryString.toString(),
            });

            if (!response.ok) {
                this.logger.info('[AuthService/handleEgAuthResponse] fetching eg token error');
                throw new Error('Code exchange request error');
            }
            const result: { access_token: string } = await response.json();

            if (!result.access_token) {
                this.logger.info('[AuthService/handleEgAuthResponse] fetching eg token - lack of access_token');
                throw new Error('AccessToken not found');
            }
            egAccessToken = result.access_token;

            return {
                egDomain,
                egAccessToken,
            };
        } catch (e) {
            this.logger.error(e, '[AuthService/handleEgAuthResponse] error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }
    }

    public async addEgAuthSetup({
        setupId,
        egDomain,
        egAccessToken,
    }: {
        setupId: string;
        egDomain: string;
        egAccessToken: string;
    }): Promise<AuthSetupReadModel> {
        let userData: EgnyteUserReadModel;
        try {
            userData = await this.egnyteService.getUserInfoByCredentials({ egDomain, egAccessToken });
        } catch (e) {
            this.logger.error(e, '[AuthService/addEgAuthSetup] Getting user info error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }

        let authSetup: AuthSetupReadModel;
        try {
            authSetup = await this.authSetupRepository.updateAuthSetup(setupId, {
                egDomain: sanitizeDomain(egDomain),
                egAccessToken,
                egUserId: String(userData.id),
                egUserType: userData.userType,
                egUsername: userData.username,
            });
        } catch (e) {
            this.logger.error(e, '[AuthService/addEgAuthSetup] error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }

        const egPrivateClientInstance = this.egPrivateClientService.getInstance(authSetup);
        const egPrivateAccessToken = await egPrivateClientInstance.getEgPrivateToken();

        try {
            authSetup = await this.authSetupRepository.updateAuthSetup(setupId, {
                egPrivateToken: egPrivateAccessToken,
            });
        } catch (e) {
            this.logger.error(e, '[AuthService/addEgAuthSetup] error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }

        return authSetup;
    }

    public async finalizeAuth(authSetupId: string): Promise<AuthReadModel> {
        let authSetup: AuthSetupReadModel;
        try {
            authSetup = await this.authSetupRepository.getById(authSetupId);
        } catch (e) {
            this.logger.error(e, '[AuthService/finalizeAuth] Getting authSetup error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }

        if (!authSetup) {
            this.logger.error('[AuthService/finalizeAuth] AuthSetup not found error');
            throw new NotFoundException('AuthSetup not found');
        }

        const userId = Buffer.from(`${authSetup.msTenantId}_${authSetup.msUserId}`).toString('base64');

        try {
            const auth = await this.authRepository.updateAuth(userId, {
                // egnyte
                egUserId: authSetup.egUserId,
                egDomain: sanitizeDomain(authSetup.egDomain),
                egUsername: authSetup.egUsername,
                egUserType: authSetup.egUserType,
                egAccessToken: authSetup.egAccessToken,
                egPrivateToken: authSetup.egPrivateToken,
                isCopilotDomain: authSetup.options?.copilotContext,

                // ms
                msUserId: authSetup.msUserId,
                msAccessToken: authSetup.msAccessToken,
                msRefreshToken: authSetup.msRefreshToken,
                msTokenExpiration: authSetup.msTokenExpiration,
                msTenantId: authSetup.msTenantId,
                ...(authSetup.msEmail && { msEmail: authSetup.msEmail }),
            });

            this.logger.info(
                {
                    userId: auth.userId,
                },
                '[AuthService/finalizeAuth] auth saved successfully'
            );

            return auth;
        } catch (e) {
            this.logger.error(e, '[AuthService/finalizeAuth] error');
            throw new InternalServerErrorException('Saving Auth error');
        }
    }

    public async loginUser(
        identity: IdentityReadModel,
        loginQuery: LoginQueryDto
    ): Promise<{ token: string; domain: string }> {
        const usePrimaryDomain = JSON.parse(loginQuery.usePrimaryDomain);

        if (!usePrimaryDomain && !loginQuery.domain) {
            // case: custom-tab; before initial association with the domain
            this.logger.error({ userId: identity.userId }, '[AuthService/loginUser] Domain not provided');
            throw new ForbiddenException('Domain not provided');
        }

        let primaryDomain: string;

        if (usePrimaryDomain) {
            const adminAuth = await this.adminAuthRepository.getByTenantId(identity.msTenantId);

            if (!adminAuth?.msSetupFinished) {
                // case: bot; no admin-setup which is required
                this.logger.info({ userId: identity.userId }, '[AuthService/loginUser] Admin setup required');

                throw new BackendException({
                    message: 'Admin setup required',
                    status: 424,
                    code: ERROR_CODE_TYPE.NOT_FOUND_ERROR,
                });
            }

            primaryDomain = adminAuth.egDomain;
        }

        const domain = usePrimaryDomain
            ? primaryDomain
            : await this.egPrivateClientService.getInstance().getEgDomainFromUrl(loginQuery.domain);

        const authData = await this.authRepository.getDomainScopedUserAuth(identity.userId, domain);

        if (!authData) {
            this.logger.error({ userId: identity.userId }, '[AuthService/loginUser] authData not found error');
            throw new ForbiddenException('Operation is forbidden');
        }

        const payload = {
            userId: authData.userId,
            egDomain: authData.egDomain,
            egUserType: authData.egUserType,
        };
        const token = this.jwtService.sign(payload, {
            secret: this.configService.get('jwtTokenSecret'),
            expiresIn: this.configService.get('jwtTokenExpiration'),
            algorithm: 'HS512',
        });

        this.logger.info({ userId: identity.userId }, '[AuthService/loginUser] token successfully created');

        return {
            token,
            domain: authData.egDomain,
        };
    }

    public async removeAuth(userId: string, domain: string): Promise<void> {
        this.logger.debug({ userId }, '[AuthService/removeAuth] Removing auth');
        await this.authRepository.removeByUserId(userId, domain);
    }

    public async getDomainEnv(egDomain: string): Promise<EGGetEnvResponse> {
        const domainInfoUrl = `https://${egDomain}/rest/public/1.0/env-pub`;
        this.logger.debug({ domainInfoUrl }, '[AuthService/getDomainEnv] Domain env url');

        const responseEnv = await fetch(domainInfoUrl);

        if (!responseEnv.ok) {
            const responseText = await responseEnv.text();

            throw new InternalServerErrorException(responseText || 'Something went wrong');
        }

        return responseEnv.json();
    }

    public async getCurrentEgUserData(): Promise<EgnyteUserReadModel> {
        return this.egnyteService.getCurrentUserInfo();
    }
}
