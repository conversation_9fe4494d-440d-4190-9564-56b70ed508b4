import { IPublic } from '../utils/public.interface';
import { RenewalHookRequestDto } from '../../incoming/hooks/dtos/renewal-hook-request.dto';
import { IUnpacked } from '../utils/unpacked.interface';

export class ResourceLifecycleEvent {
    public static fromApiResponse(data: IUnpacked<RenewalHookRequestDto['value']>): IPublic<ResourceLifecycleEvent> {
        return {
            clientState: data.clientState,
            lifecycleEvent: data.lifecycleEvent,
            subscriptionId: data.subscriptionId,
        };
    }

    public lifecycleEvent: 'reauthorizationRequired';

    public clientState: string;

    public subscriptionId: string;
}
