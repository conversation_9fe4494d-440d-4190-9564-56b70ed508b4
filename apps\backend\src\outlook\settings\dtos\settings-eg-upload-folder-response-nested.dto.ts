import { ApiProperty } from '@nestjs/swagger';
import { SettingsReadUploadFolderModel } from 'ms-metaos-database/outlook';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class SettingsEgUploadFolderResponseNestedDto {
    @ValidateArgs()
    public static fromResponse(data: SettingsReadUploadFolderModel): SettingsEgUploadFolderResponseNestedDto {
        return {
            name: data.name,
            path: data.path,
            isFolder: data.isFolder,
            folderId: data.folderId,
        };
    }

    @ApiProperty()
    public folderId: string;

    @ApiProperty()
    public path: string;

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public isFolder: boolean;
}
