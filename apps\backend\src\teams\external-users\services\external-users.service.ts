import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bullmq';
import {
    PendingExternalUserRepository,
    AdminAuthReadModel,
    PendingExternalUserReadModel,
} from 'ms-metaos-database/teams';
import { ERROR_CODE_TYPE } from 'ms-metaos-types/enums';
import { EgnyteAuthType } from 'ms-metaos-types/types';
import { BackendException } from '../../common/utils/BackendException';
import { getRedisOptions } from '../../common/config/module-config';
import { ReportingService } from '../../common/metrics/services/reporting.service';
import { filterPendingExternalUsers } from '../../common/utils/external-users';
import { ExternalUsersQueryDto } from '../dtos/external-users-query.dto';
import { CreateEgnyteUserRequestDto } from '../dtos/create-egnyte-user-request.dto';
import { UpdateExternalUserRequestDto } from '../dtos/update-external-user-request.dto';

type ValidationResult = {
    validUpdates?: {
        egUserName: string;
        egGivenName: string;
        egFamilyName: string;
        egAuthType: string;
    };
    errors?: string;
};

@Injectable()
export class ExternalUsersService {
    constructor(
        private logger: PinoLogger,
        private pendingExternalUserRepository: PendingExternalUserRepository,
        private configService: ConfigService,
        private reportingService: ReportingService
    ) {}

    public async getExternalUsers({
        adminAuth,
        query,
    }: {
        adminAuth: AdminAuthReadModel;
        query: ExternalUsersQueryDto;
    }) {
        const msUserIds = query.msUserIds || [];

        this.logger.info(
            { msUserIds },
            `ExternalUsers/getExternalUsers] Getting pending external users for msTenantId: ${adminAuth.msTenantId}`
        );

        try {
            const pendingExternalUsers = msUserIds?.length
                ? await Promise.all(
                      msUserIds.map((id: string) =>
                          this.pendingExternalUserRepository.findOne(adminAuth.msTenantId, id)
                      )
                  )
                : await this.pendingExternalUserRepository.findAllByTenantId(adminAuth.msTenantId);

            return filterPendingExternalUsers(pendingExternalUsers, adminAuth.createAccountsForExternalUsers);
        } catch (error: any) {
            this.logger.error({ error }, '[ExternalUsers/getExternalUsers] Could not get pending external users');

            throw new BackendException({
                message: 'Could not get data about pending external users',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async getExternalUserDetails({ adminAuth, msUserId }: { adminAuth: AdminAuthReadModel; msUserId: string }) {
        this.logger.info(`[ExternalUsers/getExternalUserDetails] Getting pending external user ${msUserId} - start`);

        try {
            const pendingExternalUser = await this.pendingExternalUserRepository.findOne(
                adminAuth.msTenantId,
                msUserId
            );

            if (!pendingExternalUser) {
                throw new NotFoundException('Pending External user not found');
            }

            return pendingExternalUser;
        } catch (e: any) {
            this.logger.error(
                { e },
                `[ExternalUsers/getExternalUserDetails] Could not get pending external user with msUserId: ${msUserId} from database`
            );

            throw new BackendException({
                message: 'Could not get pending external user',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async createEgnyteUsers({
        adminAuth,
        requestedUsers,
    }: {
        adminAuth: AdminAuthReadModel;
        requestedUsers: CreateEgnyteUserRequestDto[];
    }) {
        this.logger.info(
            `[ExternalUsers/createEgnyteUsers] Creating Egnyte accounts for users with email and msUserId: ${requestedUsers
                .map((user) => `${user.email}/${user.msUserId}`)
                .join(', ')}`
        );

        try {
            const users = await Promise.all(
                requestedUsers.map(async (requestedUser) => {
                    try {
                        const user = await this.pendingExternalUserRepository.findOne(
                            adminAuth.msTenantId,
                            requestedUser.msUserId
                        );
                        if (!user) {
                            this.logger.error({ requestedUser }, `ExternalUsers/createEgnyteUsers] User not found`);

                            throw new BackendException({
                                message: 'User not found',
                                status: HttpStatus.NOT_FOUND,
                                code: ERROR_CODE_TYPE.NOT_FOUND_ERROR,
                            });
                        }

                        user.errorsInfo = [];

                        return await this.pendingExternalUserRepository.update(user);
                    } catch (error: any) {
                        this.logger.error(
                            { error },
                            `[ExternalUsers/createEgnyteUsers] Could not reset errorInfo list`
                        );

                        return undefined;
                    }
                })
            );

            const connection = getRedisOptions(this.configService);
            const queue = new Queue('CreateExternalUsers', {
                prefix: this.configService.get('redisPrefix').replace(/:$/g, ''),
                connection,
            });

            await queue.addBulk(
                users
                    .filter((user: PendingExternalUserReadModel) => !!user)
                    .map((user: PendingExternalUserReadModel) => ({
                        name: `Create Egnyte User`,
                        opts: {
                            jobId: `${adminAuth.msTenantId}/${user.msUserId}`,
                            removeOnComplete: true,
                            removeOnFail: true,
                            attempts: 1,
                        },
                        data: {
                            tenantId: adminAuth.msTenantId,
                            msUserId: user.msUserId,
                            traceId: this.logger.logger.bindings().traceId,
                        },
                    }))
            );
            await queue.close();
        } catch (error: any) {
            this.logger.error(
                { error },
                '[ExternalUsers/createEgnyteUsers] Could not add new users to queue "CreateExternalUsers"'
            );

            throw new BackendException({
                message: 'Could not create Egnyte users',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async editExternalUser({
        adminAuth,
        updateExternalUserRequest,
    }: {
        adminAuth: AdminAuthReadModel;
        updateExternalUserRequest: UpdateExternalUserRequestDto;
    }) {
        this.logger.info(
            `[ExternalUsers/editExternalUser] Updating pending external user with msUserId: ${updateExternalUserRequest.msUserId} - start`
        );

        const { updates } = updateExternalUserRequest;
        const { validUpdates, errors } = this.validate(updates);

        if (errors) {
            this.logger.error(
                `[ExternalUsers/editExternalUser] Updating pending external user failed. Errors: ${errors}`
            );

            throw new BackendException({
                message: 'Update external user failed. Bad input values',
                status: HttpStatus.BAD_REQUEST,
                code: ERROR_CODE_TYPE.INVALID_REQUEST,
            });
        }

        try {
            const pendingExternalUser = await this.pendingExternalUserRepository.findOne(
                adminAuth.msTenantId,
                updateExternalUserRequest.msUserId
            );

            if (!pendingExternalUser) {
                this.logger.error(
                    `[ExternalUsers/editExternalUser] pendingExternalUser with msUserId: ${updateExternalUserRequest.msUserId} not found`
                );

                throw new BackendException({
                    message: 'Update external user failed, user not found',
                    status: HttpStatus.BAD_REQUEST,
                    code: ERROR_CODE_TYPE.INVALID_REQUEST,
                });
            }

            pendingExternalUser.egUserName = validUpdates.egUserName;
            pendingExternalUser.egGivenName = validUpdates.egGivenName;
            pendingExternalUser.egFamilyName = validUpdates.egFamilyName;
            pendingExternalUser.egAuthType = validUpdates.egAuthType as EgnyteAuthType;

            await this.pendingExternalUserRepository.update(pendingExternalUser);
        } catch (e: any) {
            this.logger.error(
                { e },
                `[ExternalUsers/editExternalUser] Could not update pending external user with msUserId: ${updateExternalUserRequest.msUserId}`
            );

            throw new BackendException({
                message: 'Could not update pending external user',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async deleteExternalUsers({ adminAuth }: { adminAuth: AdminAuthReadModel }) {
        this.logger.info(
            `[ExternalUsers/deleteExternalUsers] Deleting pending external users for msTenantId: ${adminAuth.msTenantId}`
        );

        try {
            const pendingExternalUsers: PendingExternalUserReadModel[] =
                await this.pendingExternalUserRepository.findAllByTenantId(adminAuth.msTenantId);

            const removePendingExternalUsersPromises = (pendingExternalUsers || []).map(
                async (user: PendingExternalUserReadModel) => {
                    await this.pendingExternalUserRepository.deleteOne(adminAuth.msTenantId, user.msUserId);

                    this.reportingService.processEvent({
                        userId: user.msUserId || 'n/a',
                        username: user.egUserName || 'n/a',
                        customField1: user.email || 'n/a',
                        customField2: user.msTenantId || 'n/a',
                        action: 'standardUserCreationRequestRejected',
                        statusCode: 204,
                    });
                }
            );

            await Promise.all(removePendingExternalUsersPromises);
        } catch (e: any) {
            this.logger.error({ e }, '[ExternalUsers/deleteExternalUsers] Could not delete pending external users');

            throw new BackendException({
                message: 'Could not delete pending external users',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    public async deleteExternalUser({ adminAuth, msUserId }: { adminAuth: AdminAuthReadModel; msUserId: string }) {
        if (!msUserId) {
            this.logger.error(
                '[ExternalUsers/deleteExternalUser] Could not delete pending external user. No msUserId provided'
            );

            throw new BackendException({
                message: 'Could not delete pending external user',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }

        this.logger.info(
            `[ExternalUsers/deleteExternalUser] Deleting pending external user with msUserId: ${msUserId}`
        );

        try {
            const pendingExternalUser = await this.pendingExternalUserRepository.findOne(
                adminAuth.msTenantId,
                msUserId
            );

            const reportingFields = {
                userId: pendingExternalUser.msUserId || 'n/a',
                username: pendingExternalUser.egUserName || 'n/a',
                customField1: pendingExternalUser.email || 'n/a',
                customField2: pendingExternalUser.msTenantId || 'n/a',
            };

            await this.pendingExternalUserRepository.deleteOne(adminAuth.msTenantId, msUserId);

            this.reportingService.processEvent({
                ...reportingFields,
                action: 'standardUserCreationRequestRejected',
                statusCode: 204,
            });
        } catch (e: any) {
            this.logger.error(
                { e },
                '[AdminSetup/externalUserDetailsDeleteHandler] Could not delete pending external user'
            );

            throw new BackendException({
                message: 'Could not delete pending external user',
                status: HttpStatus.INTERNAL_SERVER_ERROR,
                code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
            });
        }
    }

    private validate = (externalUserUpdates: any): ValidationResult => {
        if (!externalUserUpdates) {
            return { errors: 'No data provided' };
        }

        enum USER_EDITABLE_KEYS {
            EG_USER_NAME = 'egUserName',
            EG_GIVEN_NAME = 'egGivenName',
            EG_FAMILY_NAME = 'egFamilyName',
            EG_AUTH_TYPE = 'egAuthType',
        }

        const validUpdates = {
            egUserName: externalUserUpdates[USER_EDITABLE_KEYS.EG_USER_NAME],
            egGivenName: externalUserUpdates[USER_EDITABLE_KEYS.EG_GIVEN_NAME],
            egFamilyName: externalUserUpdates[USER_EDITABLE_KEYS.EG_FAMILY_NAME],
            egAuthType: externalUserUpdates[USER_EDITABLE_KEYS.EG_AUTH_TYPE],
        };

        const errors = [];

        Object.keys(validUpdates).forEach((key) => {
            const value = validUpdates[key];

            if (!value || typeof value !== 'string') {
                errors.push(
                    `${
                        value === ''
                            ? `No value provided for key: ${key}`
                            : `Invalid value for key: ${key}; value: ${value}`
                    }`
                );
            }
        });

        if (errors.length) {
            return { errors: errors.join(', ') };
        }

        return { validUpdates, errors: null };
    };
}
