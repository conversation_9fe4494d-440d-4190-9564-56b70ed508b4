import { IsOptional, IsString } from '@nestjs/class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';

export class FilePickerFolderInfoQueryDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public id?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public path?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @Transform((params: TransformFnParams): boolean => params?.value !== 'false')
    public withChildren = true;
}
