import { FilePickerBreadcrumbsReadModel } from './file-picker-breadcrumbs-read.model';
import { getFilePickerItemReadModelMock } from '../file-picker-item/file-picker-item-read.mock';

export function getFilePickerBreadcrumbsReadModelMock(): FilePickerBreadcrumbsReadModel {
    return {
        path: '/testBreadcrumbsParentItem/testBreadcrumbsCurrentItem',
        folderId: 'testBreadcrumbsCurrentItem',
        items: [
            getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsRootItem',
                name: 'testBreadcrumbsRootItem',
                path: '/',
            }),
            getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsParentItem',
                name: 'testBreadcrumbsParentItem',
                path: '/testBreadcrumbsParentItem',
                parentId: 'testBreadcrumbsRootItem',
            }),
            getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsCurrentItem',
                name: 'testBreadcrumbsCurrentItem',
                path: '/testBreadcrumbsParentItem/testBreadcrumbsCurrentItem',
                parentId: 'testBreadcrumbsParentItem',
            }),
        ],
    };
}
