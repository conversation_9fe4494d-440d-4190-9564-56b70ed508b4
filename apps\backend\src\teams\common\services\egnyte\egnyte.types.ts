import { ListFileRes, ListFolderRes, UserEnum } from '@integrations/egnyte-ts-sdk';

export interface EGFolderDataOptions {
    withChildren: boolean;
    count?: number;
    offset?: number;
    sortBy?: keyof ListFileRes | keyof ListFolderRes;
    sortDirection?: 'ascending' | 'descending';
}

export type EGGetFolderDataByIdParams = { id: string } & EGFolderDataOptions;
export type EGGetFolderDataByPathParams = { path: string } & EGFolderDataOptions;

export interface EGGetEnvResponse {
    ui: {
        resultsPerPage: number;
        display: { footer: boolean };
    };
    config: Record<string, string>;
    analytics: {
        utmCampaign: string;
        utmSource: string;
        utmTerm: string;
        dcName: string;
        mixPanelKey: string;
        telemetryProjectId: string;
    };
    session: {
        csrfToken: string;
        userType: UserEnum;
        userId: number;
        userLoggedIn: boolean;
    };
    features: Record<string, unknown>;
    workgroup: {
        brandSettings: {
            logoUrl: string;
            defaultLogoEnabled: boolean;
            accessURL: string;
            fileServerLabel: string;
            helpAndFaqUrl: string;
            themeSettings: {
                homeHeaderColor?: string;
                showFooter: boolean;
                showLinkFooter: boolean;
            };
        };
        accessUrl: string;
        brandSettingsEnabled: boolean;
        name: string;
        timezone: {
            offset: string;
            displayName: string;
            id: string;
        };
        schemeType: string;
        workgroupId: string;
        pid: number;
        syncUrl: string;
    };
}

export interface EGAppsProviderResponse {
    apps: EGAppsProviderIntegration[];
    appsForGroups: EGAppsProviderIntegration[];
    appsForMobilesNotSupportingCSPP: EGAppsProviderIntegration[];
    appsForGroupsForMobilesNotSupportingCSPP: EGAppsProviderIntegration[];
}

export interface EGAppsProviderIntegration {
    name: string;
    apiKey: string;
    appId: string;
    appLogo: string;
    integrations: { [action: string]: EGAppsProviderAction };
    userCanHaveSettings: boolean;
}

export interface EGAppsProviderAction {
    entryPoint: string;
    types: ('file' | 'folder')[];
    accessLevel?: string;
    serviceUrl: string;
    selectionQuantity?: number[] | string;
    invocationMethod: string;
    icon: string;
    text: string;
    scope?: string[];
    tooltip?: string;
    actionType?: string;
    extensions?: [string];
}

export const egCoeditIntegrationsMap = {
    mswopibeta: {
        Word: {
            edit: 'msBetaWordOnlineEdit',
            editdesktop: 'msBetaWordDesktopEdit',
            editnew: 'msBetaWordOnlineCreate',
        },
        Excel: {
            edit: 'msBetaExcelOnlineEdit',
            editdesktop: 'msBetaExcelDesktopEdit',
            editnew: 'msBetaExcelOnlineCreate',
        },
        PowerPoint: {
            edit: 'msBetaPowerPointOnlineEdit',
            editdesktop: 'msBetaPowerPointDesktopEdit',
            editnew: 'msBetaPowerPointOnlineCreate',
        },
    },
    mssharepointembedded: {
        Word: {
            edit: 'msWordSharepointOnlineEdit',
            editdesktop: 'msWordSharepointDesktopEdit',
            editnew: 'msWordSharepointCreate',
        },
        Excel: {
            edit: 'msExcelSharepointOnlineEdit',
            editdesktop: 'msExcelSharepointDesktopEdit',
            editnew: 'msExcelSharepointCreate',
        },
        PowerPoint: {
            edit: 'msPowerPointSharepointOnlineEdit',
            editdesktop: 'msPowerPointSharepointDesktopEdit',
            editnew: 'msPowerPointSharepointCreate',
        },
    },
};
