import { EgnyteAuthService } from './egnyte-auth.service';
import { IPublic } from '../../common/utils/public.interface';
import { IEgnyteUserInfo } from '../../common/interfaces/egnyte-provider.interface';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { getAuthEGReadModelMock } from '../models/auth-eg/auth-eg-read.mock';
import { getUserDetailsModelMock } from '../../common/models/user-details/user-details.mock';
import { Mappers } from '../../common/utils/mappers';

export function getEgUserInfoMock(): IEgnyteUserInfo {
    return {
        id: UNIT_TEST_VARS.egId,
        username: UNIT_TEST_VARS.egUsername,
        email: UNIT_TEST_VARS.egEmail,
        first_name: UNIT_TEST_VARS.egFirstName,
        last_name: UNIT_TEST_VARS.egLastName,
        user_type: Mappers.mapModelToRawUserType(UNIT_TEST_VARS.egUserType),
    };
}

export function getEgnyteAuthServiceMock(): IPublic<EgnyteAuthService> {
    return {
        getAuthUrl: jest.fn().mockReturnValue(UNIT_TEST_VARS.services.egnyteAuthService.getAuthUrl),
        handleAuthCode: jest.fn().mockResolvedValue(getAuthEGReadModelMock()),
        getEgUserInfo: jest.fn().mockResolvedValue(getUserDetailsModelMock()),
    };
}
