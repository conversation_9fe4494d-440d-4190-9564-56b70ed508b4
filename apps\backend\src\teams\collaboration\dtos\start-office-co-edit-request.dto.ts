import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUrl } from '@nestjs/class-validator';
import { EG_WOPI_ACTION_TYPE } from 'ms-metaos-types/enums';

export class StartOfficeCoEditRequestDto {
    @ApiProperty({ example: 'https://domain.egnyte.com/navigate/file/file-id-uuid4' })
    @IsUrl()
    public openInEgnyteUrl: string;

    @ApiProperty({ enum: EG_WOPI_ACTION_TYPE, type: String, example: EG_WOPI_ACTION_TYPE.EDIT })
    @IsEnum(EG_WOPI_ACTION_TYPE)
    public action: EG_WOPI_ACTION_TYPE;

    @ApiProperty({ enum: ['nested', 'tab', 'desktop'], type: String, example: 'nested', required: false })
    @IsOptional()
    @IsEnum(['nested', 'tab', 'desktop'])
    public target?: 'nested' | 'tab' | 'desktop';
}
