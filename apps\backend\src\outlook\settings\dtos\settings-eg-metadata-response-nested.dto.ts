import { ApiProperty } from '@nestjs/swagger';
import { SettingsReadMetadataModel } from 'ms-metaos-database/outlook';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class SettingsEgMetadataResponseNestedDto {
    @ValidateArgs()
    public static fromResponse(data: SettingsReadMetadataModel): SettingsEgMetadataResponseNestedDto {
        return {
            isNamespaceAdded: data.isNamespaceAdded,
            isSavingEnabled: data.isSavingEnabled,
        };
    }

    @ApiProperty()
    public isSavingEnabled: boolean;

    @ApiProperty()
    public isNamespaceAdded: boolean;
}
