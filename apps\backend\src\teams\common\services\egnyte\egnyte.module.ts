import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseAdminAuthModule } from 'ms-metaos-database/teams';
import { EgnytePublicClientService } from './egnyte-public-client.service';
import { EgnyteService } from './egnyte.service';
import { EgnyteLinksHelperService } from './egnyte-links-helper.service';

@Module({
    imports: [DatabaseAdminAuthModule],
    providers: [EgnytePublicClientService, EgnyteLinksHelperService, EgnyteService],
    exports: [EgnyteService],
})
export class EgnyteModule {}
