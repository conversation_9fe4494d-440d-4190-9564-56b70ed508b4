import { Injectable } from '@nestjs/common';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { UploadLinkResponseDto } from '../dtos/upload-link-response.dto';
import { Helpers } from '../../common/utils/helpers';
import { UploadLinkCreateModel } from '../models/upload-link-create.model';

@Injectable()
export class UploadLinkService {
    constructor(private readonly egnyteClientService: EgnyteClientService) {}

    public async createUploadLink(uploadLinkCreateModel: UploadLinkCreateModel): Promise<UploadLinkResponseDto> {
        const response = await this.egnyteClientService.createUploadLink({
            path: uploadLinkCreateModel.item.path,
            folderPerRecipient: uploadLinkCreateModel.item.folderPerRecipient,
            expiryDate: Helpers.calculateFutureDate(uploadLinkCreateModel.expiration),
        });
        const { url } = response.links.pop();
        const { path } = response;

        return {
            url,
            name: Helpers.getNameFromPath(path),
            type: 'uploadFolder',
        };
    }
}
