import { <PERSON><PERSON><PERSON>, Scope } from '@nestjs/common';
import { DatabaseMsAmUrlModule } from 'ms-metaos-database/outlook';
import { MsAmUrlService } from './ms-am-url.service';

@Module({
    imports: [DatabaseMsAmUrlModule],
    providers: [MsAmUrlService, { provide: 'MS_AM_URL_CACHE', useValue: null, scope: Scope.DEFAULT }],
    exports: [MsAmUrlService],
})
export class MsAmUrlServiceModule {}
