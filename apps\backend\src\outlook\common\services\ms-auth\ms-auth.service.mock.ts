import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import { MsAuthService } from './ms-auth.service';
import { IPublic } from '../../utils/public.interface';
import { UNIT_TEST_VARS } from '../../utils/unit-test-vars';

function getTokenResponseMock(): IGetTokenResponse {
    return {
        accessToken: 'testAccessToken',
        expireTime: 1000,
        refreshToken: 'testRefreshToken',
        scopes: ['testScope1', 'testScope2'],
        requestId: 'testRequestId',
    };
}

function getInitialAuthDataMock() {
    return {
        clientId: '1234',
        scopes: [],
    };
}

export function getMsAuthServiceMock(): IPublic<MsAuthService> {
    return {
        isUserEmailValid: jest.fn().mockResolvedValue(UNIT_TEST_VARS.services.msAuthService.isUserEmailValid),
        getRedirectToMsAuthorize: jest
            .fn()
            .mockReturnValue(UNIT_TEST_VARS.services.msAuthService.getRedirectToMsAuthorize),
        getRedirectToConsent: jest.fn().mockReturnValue(UNIT_TEST_VARS.services.msAuthService.getRedirectToConsent),
        handleOnBehalfOf: jest.fn().mockResolvedValue(getTokenResponseMock()),
        handleAuthCode: jest.fn().mockResolvedValue(getTokenResponseMock()),
        getValidToken: jest.fn().mockResolvedValue(getTokenResponseMock()),
        getInitialAuthData: jest.fn().mockResolvedValue(getInitialAuthDataMock()),
    };
}
