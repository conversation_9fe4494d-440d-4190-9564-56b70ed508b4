// eslint-disable-next-line max-classes-per-file
import { SHARE_LINK_TYPE } from '../../enums/share-link-type.enum';
import { SHARE_LINK_EXPIRATION_UNIT } from '../../enums/share-link-expiration-unit.enum';
import {
    EGNYTE_LINK_TYPE,
    EGNYTE_RESTRICTION_TYPE,
    IEgnyteAllowedLinkTypes,
    IEgnyteDefaultAttributes,
    IEgnytePerms,
    IEgnyteSdkResult,
    IEgnyteSharingRestrictions,
} from '../../../common/interfaces/egnyte-provider.interface';
import { IPintLinkDomainSettings } from '../../../common/interfaces/pint-link.interface';
import { Mappers } from '../../../common/utils/mappers';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

interface IShareLinkPermission {
    linkTypes: Partial<{ [key in SHARE_LINK_TYPE]: boolean }>;
    default: SHARE_LINK_TYPE | null;
}

class ShareLinkPermissionsReadPermissionsModel {
    @ValidateArgs()
    public static fromFolderDataResponse(
        data: IEgnyteSdkResult<IEgnyteAllowedLinkTypes & IEgnytePerms>,
        supportedShareLinks: SHARE_LINK_TYPE[]
    ): ShareLinkPermissionsReadPermissionsModel {
        return {
            linkTypes: ShareLinkPermissionsReadPermissionsModel.mapShareLinkPermissions(
                supportedShareLinks,
                ShareLinkPermissionsReadPermissionsModel.mapResponseAllowedLinkTypesToModel(
                    data.allowed_folder_link_types
                )
            ),
        };
    }

    @ValidateArgs()
    public static fromSharingRestrictionsResponse(
        sharingRestrictions: IEgnyteSharingRestrictions,
        supportedShareLinks: SHARE_LINK_TYPE[]
    ): ShareLinkPermissionsReadPermissionsModel {
        return {
            linkTypes: ShareLinkPermissionsReadPermissionsModel.mapAccessibilityToPermissions(
                supportedShareLinks,
                sharingRestrictions.accessibility
            ),
        };
    }

    private static mapResponseAllowedLinkTypesToModel(types: EGNYTE_LINK_TYPE[]): SHARE_LINK_TYPE[] {
        return types.reduce((acc: SHARE_LINK_TYPE[], type: EGNYTE_LINK_TYPE) => {
            try {
                acc.push(Mappers.mapRawToModelLinkType(type));

                return acc;
            } catch (e) {
                return acc;
            }
        }, []);
    }

    private static mapShareLinkPermissions(
        supportedLinks: SHARE_LINK_TYPE[],
        allowedLinks: SHARE_LINK_TYPE[]
    ): Partial<{ [key in SHARE_LINK_TYPE]: boolean }> {
        return supportedLinks.reduce((acc, value: SHARE_LINK_TYPE) => {
            switch (value) {
                case SHARE_LINK_TYPE.DIRECT:
                    acc[value] = supportedLinks.includes(SHARE_LINK_TYPE.DIRECT);
                    break;
                default:
                    acc[value] = allowedLinks.includes(value);
                    break;
            }

            return acc;
        }, {});
    }

    private static mapAccessibilityToPermissions(
        supportedLinks: SHARE_LINK_TYPE[],
        accessibility: IEgnyteSharingRestrictions['accessibility']
    ): Partial<{ [key in SHARE_LINK_TYPE]: boolean }> {
        return supportedLinks.reduce((acc, value: SHARE_LINK_TYPE) => {
            switch (value) {
                case SHARE_LINK_TYPE.DIRECT:
                    acc[value] = supportedLinks.includes(SHARE_LINK_TYPE.DIRECT);
                    break;
                default:
                    acc[value] = accessibility[value]?.type !== EGNYTE_RESTRICTION_TYPE.BLOCK;
                    break;
            }

            return acc;
        }, {});
    }

    public linkTypes: IShareLinkPermission['linkTypes'];
}

class ShareLinkPermissionsReadDefaultsModel {
    public static fromDefaultAttributesResponse(
        defaultAttributes: IEgnyteDefaultAttributes,
        sharingRestrictions: IEgnyteSharingRestrictions
    ): ShareLinkPermissionsReadDefaultsModel {
        return {
            linkTypes: {
                anyone: sharingRestrictions.accessibility.anyone?.type !== EGNYTE_RESTRICTION_TYPE.BLOCK,
                domain: sharingRestrictions.accessibility.domain?.type !== EGNYTE_RESTRICTION_TYPE.BLOCK,
                direct: true,
                recipients: sharingRestrictions.accessibility.recipients?.type !== EGNYTE_RESTRICTION_TYPE.BLOCK,
                password: sharingRestrictions.accessibility.password?.type !== EGNYTE_RESTRICTION_TYPE.BLOCK,
            },
            default: Mappers.mapRawToModelLinkType(defaultAttributes.accessibility),
        };
    }

    public static fromDomainSettingsResponse(data: IPintLinkDomainSettings): ShareLinkPermissionsReadDefaultsModel {
        return {
            linkTypes: data.folder.linkTypes,
            default: Mappers.mapRawToModelLinkType(data.folder.default),
        };
    }

    public linkTypes: IShareLinkPermission['linkTypes'];

    public default: IShareLinkPermission['default'];
}

export type ShareLinkPermissionsReadExpirationMaxAllowedModel = null | {
    expiryTimeValue: number;
    expiryTimeUnit:
        | SHARE_LINK_EXPIRATION_UNIT.DAYS
        | SHARE_LINK_EXPIRATION_UNIT.WEEKS
        | SHARE_LINK_EXPIRATION_UNIT.MONTHS;
    expiryClicks: number;
};

export type ShareLinkPermissionsReadExpirationDefaultModel = null | {
    value: number;
    unit: SHARE_LINK_EXPIRATION_UNIT;
};

export class ShareLinkPermissionsReadExpirationModel {
    @ValidateArgs()
    public static fromResponse(
        defaultAttributes: IEgnyteDefaultAttributes,
        sharingRestrictions: IEgnyteSharingRestrictions,
        supportedLinks: SHARE_LINK_TYPE[]
    ): ShareLinkPermissionsReadExpirationModel {
        return {
            default: supportedLinks.reduce((acc, value: SHARE_LINK_TYPE) => {
                switch (value) {
                    case SHARE_LINK_TYPE.ANYONE:
                    case SHARE_LINK_TYPE.PASSWORD:
                        acc[value] = Mappers.mapRawToModelExpirationDefaultValue(defaultAttributes.publicLinks);
                        break;
                    case SHARE_LINK_TYPE.DOMAIN:
                    case SHARE_LINK_TYPE.RECIPIENTS:
                        acc[value] = Mappers.mapRawToModelExpirationDefaultValue(defaultAttributes.privateLinks);
                        break;
                    default:
                        break;
                }

                return acc;
            }, {}),
            maxAllowed: supportedLinks.reduce((acc, value: SHARE_LINK_TYPE) => {
                switch (value) {
                    case SHARE_LINK_TYPE.ANYONE:
                    case SHARE_LINK_TYPE.PASSWORD:
                        acc[value] = Mappers.mapRawToModelExpirationMaxValue(sharingRestrictions.publicLinks);
                        break;
                    case SHARE_LINK_TYPE.DOMAIN:
                    case SHARE_LINK_TYPE.RECIPIENTS:
                        acc[value] = Mappers.mapRawToModelExpirationMaxValue(sharingRestrictions.privateLinks);
                        break;
                    default:
                        break;
                }

                return acc;
            }, {}),
        };
    }

    public maxAllowed: { [key in SHARE_LINK_TYPE]?: ShareLinkPermissionsReadExpirationMaxAllowedModel };

    public default: { [key in SHARE_LINK_TYPE]?: ShareLinkPermissionsReadExpirationDefaultModel };
}

export class ShareLinkPermissionsReadModel {
    @ValidateArgs()
    public static fromResponse(
        supportedShareLinks: SHARE_LINK_TYPE[],
        responses: {
            defaultAttributes?: IEgnyteDefaultAttributes;
            sharingRestrictions?: IEgnyteSharingRestrictions;
            folderData?: IEgnyteSdkResult<IEgnyteAllowedLinkTypes & IEgnytePerms>;
            domainSettings?: IPintLinkDomainSettings;
        }
    ): ShareLinkPermissionsReadModel {
        let permissions: ShareLinkPermissionsReadPermissionsModel;
        const { defaultAttributes, sharingRestrictions, folderData, domainSettings } = responses;
        if (sharingRestrictions || folderData) {
            permissions = folderData
                ? ShareLinkPermissionsReadPermissionsModel.fromFolderDataResponse(folderData, supportedShareLinks)
                : ShareLinkPermissionsReadPermissionsModel.fromSharingRestrictionsResponse(
                      sharingRestrictions,
                      supportedShareLinks
                  );
        } else {
            permissions = {
                linkTypes: { direct: true },
            };
        }

        let defaults: ShareLinkPermissionsReadDefaultsModel;
        if (defaultAttributes || domainSettings) {
            defaults = domainSettings
                ? ShareLinkPermissionsReadDefaultsModel.fromDomainSettingsResponse(domainSettings)
                : ShareLinkPermissionsReadDefaultsModel.fromDefaultAttributesResponse(
                      defaultAttributes,
                      sharingRestrictions
                  );
        } else {
            defaults = {
                linkTypes: { direct: true },
                default: SHARE_LINK_TYPE.DIRECT,
            };
        }

        const expiration: ShareLinkPermissionsReadExpirationModel = defaultAttributes
            ? ShareLinkPermissionsReadExpirationModel.fromResponse(
                  defaultAttributes,
                  sharingRestrictions,
                  supportedShareLinks
              )
            : {
                  maxAllowed: { direct: null },
                  default: { direct: null },
              };

        return {
            permissions,
            defaults,
            expiration,
        };
    }

    public permissions: ShareLinkPermissionsReadPermissionsModel;

    public defaults: ShareLinkPermissionsReadDefaultsModel;

    public expiration: ShareLinkPermissionsReadExpirationModel;
}
