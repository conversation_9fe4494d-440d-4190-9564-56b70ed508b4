import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response, NextFunction } from 'express';
import { ReportingService } from '../services/reporting.service';

@Injectable()
export class ReportingMiddleware implements NestMiddleware {
    private readonly reportingMiddleware: (req: Request, res: Response, next: NextFunction) => void;

    constructor(configService: ConfigService, reportingService: ReportingService) {
        if (configService.get('reporting.enabled')) {
            this.reportingMiddleware = reportingService.getMiddleware();
        }
    }

    public use(req: Request, res: Response, next: NextFunction): void {
        if (this.reportingMiddleware) {
            this.reportingMiddleware(req, res, next);
        }
    }
}
