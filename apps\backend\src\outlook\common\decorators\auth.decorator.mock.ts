import { AuthReadModel } from 'ms-metaos-database/outlook';
import { EG_USER_TYPE } from '../consts/egnyte-user-type.const';

export function getAuthDataMock(): AuthReadModel {
    return {
        egnyte: {
            domain: 'testDomainString',
            accessToken: 'testAccessTokenString',
            id: 100,
            username: 'testUsername',
            userType: EG_USER_TYPE.ADMIN,
        },
        microsoft: {
            tenantId: 'testTenantIdString',
            accessToken: 'testAccessTokenString',
            refreshToken: 'testRefreshTokenString',
            tokenExpiration: 123456,
        },
        createdAt: new Date(),
        userId: 'testUserIdString',
    };
}
