import { <PERSON>, Get, Param, Res, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtGuard } from '../../../common/guards/jwt.guard';
import { FileDownloadService } from '../services/file-download.service';

@Controller('teams/filesystem/download')
@ApiTags('FileDownloadController')
@UseGuards(JwtGuard)
export class FileDownloadController {
    constructor(private readonly fileDownloadService: FileDownloadService) {}

    @Get('/id/:itemId')
    @ApiOkResponse({ type: ReadableStream })
    async downloadFile(@Param('itemId') itemId, @Res() res: Response) {
        const file = await this.fileDownloadService.downloadFile(itemId);

        file.pipe(res);
    }
}
