import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from '@nestjs/class-validator';
import { UploadAttachmentsAttachmentNestedDto } from './upload-attachments-attachment-nested.dto';
import { UploadAttachmentsMessageNestedDto } from './upload-attachments-message-nested.dto';

export class UploadAttachmentsRequestDto {
    @ApiProperty({ type: UploadAttachmentsMessageNestedDto })
    @Type(() => UploadAttachmentsMessageNestedDto)
    @ValidateNested()
    public readonly message: UploadAttachmentsMessageNestedDto;

    @ApiProperty({ type: UploadAttachmentsAttachmentNestedDto, isArray: true })
    @Type(() => UploadAttachmentsAttachmentNestedDto)
    @ValidateNested({ each: true })
    public readonly attachments: UploadAttachmentsAttachmentNestedDto[];
}
