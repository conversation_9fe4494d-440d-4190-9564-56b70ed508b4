import { PinoLogger } from 'nestjs-pino';
import { InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { egCoeditIntegrationsMap, EGAppsProviderIntegration } from '../../common/services/egnyte/egnyte.types';

export function getInvokeUrlCreateNew({
    appDefinition,
    editor,
    logger,
}: {
    appDefinition: EGAppsProviderIntegration;
    editor: 'Word' | 'Excel' | 'PowerPoint';
    logger: PinoLogger;
}) {
    if (!editor) {
        throw new InternalServerErrorException('Editor app not provided');
    }

    const action = 'editnew';
    const integrationName: string = egCoeditIntegrationsMap[appDefinition.appId][editor][action];

    if (!appDefinition.integrations[integrationName]) {
        logger.error(`[getInvokeUrlEdit] integration "${integrationName}" not found in "${appDefinition.appId}"`);
        throw new NotFoundException('Integration to create new file is not available');
    }

    const invokeUrl = appDefinition.integrations[integrationName].serviceUrl;

    logger.debug('[getCreateNewInvokeUrl] received invoke URL from app definition', {
        appId: appDefinition.appId,
        integrationName,
        invokeUrl,
    });

    return invokeUrl;
}
