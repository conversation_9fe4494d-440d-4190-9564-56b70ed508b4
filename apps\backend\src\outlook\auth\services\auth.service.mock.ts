import { getAuthReadModelMock, getAuthSetupReadModelMock } from 'ms-metaos-database/outlook';
import { AuthService } from './auth.service';
import { IPublic } from '../../common/utils/public.interface';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';

export function getAuthServiceMock(): IPublic<AuthService> {
    return {
        login: jest.fn().mockResolvedValue(UNIT_TEST_VARS.accessToken),
        logout: jest.fn(),
        getAuthSetup: jest.fn().mockResolvedValue(getAuthSetupReadModelMock()),
        setupAuthData: jest.fn().mockResolvedValue(getAuthSetupReadModelMock()),
        addMsAuthSetup: jest.fn(),
        addEgAuthSetup: jest.fn(),
        removeMsAuthSetup: jest.fn(),
        finalizeAuth: jest.fn().mockResolvedValue(getAuthReadModelMock()),
        getAuth: jest.fn().mockResolvedValue(getAuthReadModelMock()),
    };
}
