import { Test, TestingModule } from '@nestjs/testing';
import { Pi<PERSON>Logger } from 'nestjs-pino';
import { GoneException } from '@nestjs/common';
import {
    getAuthReadModelMock,
    getAuthSetupCreateModelMock,
    getAuthSetupReadModelMock,
} from 'ms-metaos-database/outlook';
import { AuthController } from './auth.controller';
import { EgnyteAuthService } from '../services/egnyte-auth.service';
import { AuthService } from '../services/auth.service';
import { MsAuthService } from '../../common/services/ms-auth/ms-auth.service';
import { AppRelative } from '../../common/utils/app-relative';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getEgnyteAuthServiceMock } from '../services/egnyte-auth.service.mock';
import { getAuthServiceMock } from '../services/auth.service.mock';
import { getMsAuthServiceMock } from '../../common/services/ms-auth/ms-auth.service.mock';
import { getAppRelativeMock, getAppRelativePartsMock } from '../../common/utils/app-relative.mock';
import { AuthRedirectResponseDto } from '../dtos/auth-redirect-response.dto';
import { AuthInitResponseDto } from '../dtos/auth-init-response.dto';
import { AuthSetupRequestDto } from '../dtos/auth-setup-request.dto';
import { getIdentityModelMock } from '../../common/models/identity/identity.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { AuthCodeQueryDto } from '../dtos/auth-code-query.dto';
import { AuthMSCodeModel } from '../models/auth-ms/auth-ms-code.model';
import { getAuthEGCreateModelMock } from '../models/auth-eg/auth-eg-create.mock';
import { AuthLoginResponseDto } from '../dtos/auth-login-response.dto';
import { AuthUserDetailsResponseDto } from '../dtos/auth-user-details-response.dto';

describe('AuthController', () => {
    const { domainMock, basePathMock, clientPathMock } = getAppRelativePartsMock();

    let egnyteAuthService: EgnyteAuthService;
    let authService: AuthService;
    let msAuthService: MsAuthService;
    let appRelative: AppRelative;
    let logger: PinoLogger;

    let authController: AuthController;

    beforeEach(async () => {
        jest.resetAllMocks();

        const testingModule: TestingModule = await Test.createTestingModule({
            providers: [
                { provide: EgnyteAuthService, useValue: getEgnyteAuthServiceMock() },
                { provide: AuthService, useValue: getAuthServiceMock() },
                { provide: MsAuthService, useValue: getMsAuthServiceMock() },
                { provide: AppRelative, useValue: getAppRelativeMock() },
                { provide: PinoLogger, useValue: getLoggerMock() },
            ],
            controllers: [AuthController],
        }).compile();

        egnyteAuthService = testingModule.get<EgnyteAuthService>(EgnyteAuthService);
        authService = testingModule.get<AuthService>(AuthService);
        msAuthService = testingModule.get<MsAuthService>(MsAuthService);
        appRelative = testingModule.get<AppRelative>(AppRelative);
        logger = testingModule.get<PinoLogger>(PinoLogger);

        authController = testingModule.get<AuthController>(AuthController);
    });

    describe('setup', () => {
        it('should successfully create authSetup, and then return response with userId', async () => {
            const authSetupResponse = getAuthSetupReadModelMock();
            const expected = AuthInitResponseDto.fromResponse(authSetupResponse);

            const identityModel = getIdentityModelMock();
            const authSetupRequestDto: AuthSetupRequestDto = {
                email: UNIT_TEST_VARS.msEmail,
                assertion: UNIT_TEST_VARS.msAssertion,
            };

            const authSetupCreateModelMock = getAuthSetupCreateModelMock();

            const result = await authController.setup(identityModel, authSetupRequestDto);

            expect(authService.setupAuthData).toHaveBeenCalledWith(authSetupCreateModelMock);
            expect(result).toEqual(expected);
        });
    });

    describe('startAuth', () => {
        it('should successfully redirect to MS Authorize endpoint without assertion value', async () => {
            const authSetupReadModelMock = getAuthSetupReadModelMock({ withoutMsAssertion: true });
            (authService.getAuthSetup as jest.Mock).mockResolvedValue(authSetupReadModelMock);

            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: UNIT_TEST_VARS.services.msAuthService.getRedirectToMsAuthorize,
            };
            const result = await authController.startAuth(UNIT_TEST_VARS.userId);

            expect(msAuthService.getRedirectToMsAuthorize).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });

        it('should successfully redirect to MS Authorize endpoint with assertion value', async () => {
            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: UNIT_TEST_VARS.services.egnyteAuthService.getAuthUrl,
            };

            const result = await authController.startAuth(UNIT_TEST_VARS.userId);

            expect(egnyteAuthService.getAuthUrl).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });

        it('should successfully redirect to MS Authorize endpoint, because MSAuthSetup was not added successfully', async () => {
            (authService.addMsAuthSetup as jest.Mock).mockRejectedValue(new Error('Error not related to MSAuthSetup'));
            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: UNIT_TEST_VARS.services.msAuthService.getRedirectToMsAuthorize,
            };

            const result = await authController.startAuth(UNIT_TEST_VARS.userId);

            expect(msAuthService.getRedirectToMsAuthorize).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
            expect(logger.error).toHaveBeenCalled();
        });

        it('should not redirect to MS Authorize endpoint, because AuthSetup has expired', async () => {
            (authService.addMsAuthSetup as jest.Mock).mockRejectedValue(new GoneException('AuthSetup expired'));

            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: `${domainMock}/${basePathMock}/${clientPathMock}/loginConfirmation?${new URLSearchParams({
                    error: 'Authorization process expired',
                }).toString()}`,
            };

            const result = await authController.startAuth(UNIT_TEST_VARS.userId);

            expect(msAuthService.getRedirectToMsAuthorize).toHaveBeenCalledTimes(0);
            expect(result).toEqual(expected);
            expect(logger.error).toHaveBeenCalled();
        });
    });

    describe('getMsCode', () => {
        it('should successfully redirect to eg-auth-finish', async () => {
            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: UNIT_TEST_VARS.services.egnyteAuthService.getAuthUrl,
            };

            const authCodeQueryDto: AuthCodeQueryDto = {
                code: UNIT_TEST_VARS.msAuthCode,
                state: UNIT_TEST_VARS.userId,
            };
            const result = await authController.getMsCode(authCodeQueryDto);

            const authMSCodeModel: AuthMSCodeModel = {
                code: authCodeQueryDto.code,
                tenantId: UNIT_TEST_VARS.msTenantId,
                redirectUri: `${domainMock}/${basePathMock}/auth/ms-auth-finish`,
            };

            expect(appRelative.fullyQualified).toHaveBeenCalledTimes(2);
            expect(egnyteAuthService.getAuthUrl).toHaveBeenCalledWith(
                authCodeQueryDto.state,
                `${domainMock}/${basePathMock}/auth/eg-auth-finish`
            );
            expect(authService.getAuthSetup).toHaveBeenCalledWith(authCodeQueryDto.state);
            expect(msAuthService.handleAuthCode).toHaveBeenCalledWith(authMSCodeModel);
            expect(authService.addMsAuthSetup).toHaveBeenCalled();
            expect(result).toEqual(expected);
        });

        it('should redirect to the login confirmation with an error, because of MSAuth error', async () => {
            const authCodeQueryDto: AuthCodeQueryDto = {
                code: UNIT_TEST_VARS.msAuthCode,
                state: UNIT_TEST_VARS.userId,
                error: UNIT_TEST_VARS.msAuthError,
            };

            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: `${domainMock}/${basePathMock}/${clientPathMock}/loginConfirmation?error=${authCodeQueryDto.error}`,
            };
            const result = await authController.getMsCode(authCodeQueryDto);

            expect(logger.warn).toHaveBeenCalled();
            expect(appRelative.fullyQualifiedClient).toHaveBeenCalled();
            expect(result).toEqual(expected);
        });
    });

    describe('getCode', () => {
        it('should successfully redirect to login confirmation after saving data', async () => {
            const authCodeQueryDto: AuthCodeQueryDto = {
                code: UNIT_TEST_VARS.msAuthCode,
                state: UNIT_TEST_VARS.userId,
                domain: UNIT_TEST_VARS.egDomain,
            };

            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: `${domainMock}/${basePathMock}/${clientPathMock}/loginConfirmation`,
            };

            const result = await authController.getCode(authCodeQueryDto);

            expect(appRelative.fullyQualified).toHaveBeenCalled();
            expect(appRelative.fullyQualifiedClient).toHaveBeenCalled();
            expect(egnyteAuthService.handleAuthCode).toHaveBeenCalledWith(getAuthEGCreateModelMock());
            expect(authService.addEgAuthSetup).toHaveBeenCalled();
            expect(result).toEqual(expected);
        });
    });

    describe('getMsConsent', () => {
        it('should successfully return the redirect response with MS consent url', async () => {
            const expected: AuthRedirectResponseDto = {
                statusCode: 302,
                url: UNIT_TEST_VARS.services.msAuthService.getRedirectToConsent,
            };

            const result = await authController.getMsConsent();

            expect(appRelative.fullyQualifiedClient).toHaveBeenCalled();
            expect(msAuthService.getRedirectToConsent).toHaveBeenCalledWith({
                redirectUri: `${domainMock}/${basePathMock}/${clientPathMock}/loginConfirmation`,
            });
            expect(result).toEqual(expected);
        });
    });

    describe('login', () => {
        it('should successfully login a user', async () => {
            const expected: AuthLoginResponseDto = {
                token: UNIT_TEST_VARS.accessToken,
            };

            const result = await authController.login(getIdentityModelMock());

            expect(result).toEqual(expected);
        });
    });

    describe('logout', () => {
        it('should successfully logout a user', async () => {
            await authController.logout(getIdentityModelMock());

            expect(authService.logout).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
        });
    });

    describe('getUserInfo', () => {
        it('should successfully return logged user info', async () => {
            const expected: AuthUserDetailsResponseDto = {
                egUsername: UNIT_TEST_VARS.egUsername,
                egEmail: UNIT_TEST_VARS.egEmail,
                egFirstName: UNIT_TEST_VARS.egFirstName,
                egLastName: UNIT_TEST_VARS.egLastName,
                egUserType: UNIT_TEST_VARS.egUserType,
                egDomain: UNIT_TEST_VARS.egDomain,
            };

            const result = await authController.getUserInfo(getAuthReadModelMock());

            expect(result).toEqual(expected);
        });
    });
});
