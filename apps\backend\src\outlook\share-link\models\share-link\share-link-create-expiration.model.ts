import { SHARE_LINK_EXPIRATION_UNIT } from '../../enums/share-link-expiration-unit.enum';
import { ShareLinkExpirationNestedDto } from '../../dtos/share-link-expiration-nested.dto';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';
import { Mappers } from '../../../common/utils/mappers';

export class ShareLinkCreateExpirationModel {
    @ValidateArgs()
    public static fromDto(data: ShareLinkExpirationNestedDto): ShareLinkCreateExpirationModel {
        return {
            isActive: data.isActive,
            ...(data.isActive && { unitType: Mappers.mapRawToModelExpirationUnitType(data.unitType) }),
            ...(data.isActive && { value: data.value }),
            ...(data.isActive && { timezone: data.timezone }),
        };
    }

    public isActive: boolean;

    public unitType?: SHARE_LINK_EXPIRATION_UNIT;

    public value?: number;

    public timezone?: string;
}
