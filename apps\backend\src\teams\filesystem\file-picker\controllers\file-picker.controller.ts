import { Controller, Delete, Get, HttpCode, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiNoContentResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ListFileRes, ListFolderRes } from '@integrations/egnyte-ts-sdk';
import { AuthReadModel } from 'ms-metaos-database/teams';
import { FilePickerFolderResponseDto } from '../dtos/file-picker-folder-response.dto';
import { FilePickerFolderQueryDto, SORT_BY } from '../dtos/file-picker-folder-query.dto';
import { FilePickerService } from '../services/file-picker.service';
import { JwtGuard } from '../../../common/guards/jwt.guard';
import { FilePickerUserFolderPermissionResponseDto } from '../dtos/file-picker-user-folder-permission-response.dto';
import { FilePickerCreateFolderResponseDto } from '../dtos/file-picker-create-folder-response.dto';
import { FilePickerRecentFileResponseDto } from '../dtos/file-picker-recent-file-response.dto';
import { Auth } from '../../../common/decorators/auth.decorator';
import { FilePickerSearchQueryDto } from '../dtos/file-picker-search-query.dto';
import { FilePickerSearchResponseDto } from '../dtos/file-picker-search-response.dto';
import { FilePickerFolderBreadcrumbQueryDto } from '../dtos/file-picker-folder-breadcrumb-query.dto';
import { FilePickerFolderBreadcrumbResponseDto } from '../dtos/file-picker-folder-breadcrumb-response.dto';

@Controller('teams/filesystem')
@ApiTags('FilePickerController')
@UseGuards(JwtGuard)
export class FilePickerController {
    constructor(private readonly filePickerService: FilePickerService) {}

    @Get('/folder')
    @ApiOkResponse({ type: FilePickerFolderResponseDto })
    public async getFolderData(@Query() query: FilePickerFolderQueryDto): Promise<FilePickerFolderResponseDto> {
        return this.filePickerService.getFolderData({
            ...(query.id && { id: query.id }),
            ...(query.path && { path: query.path }),
            withChildren: query.withChildren,
            ...(query.itemsToReturn !== undefined && { count: query.itemsToReturn }),
            ...(query.offset !== undefined && { offset: query.offset }),
            ...(query.sort_by &&
                query.sort_by !== SORT_BY.initial && {
                    sortBy: query.sort_by as keyof ListFileRes | keyof ListFolderRes,
                }),
            ...(query.sort_direction && { sortDirection: query.sort_direction }),
        });
    }

    @Get('/permissions/folder/:folderPath')
    @ApiOkResponse({ type: FilePickerUserFolderPermissionResponseDto })
    public async getUserFolderPermission(
        @Auth() user: AuthReadModel,
        @Param('folderPath') folderPath: string
    ): Promise<FilePickerUserFolderPermissionResponseDto> {
        const result = await this.filePickerService.getFolderPermissions({
            egUsername: user.egUsername,
            folderPath,
        });

        return {
            permissions: result.permissions,
            permissionActions: [...(result.permissionActions ?? [])],
        };
    }

    @Post('/folder/:folderPath')
    @ApiOkResponse({ type: FilePickerCreateFolderResponseDto })
    public async createFolder(@Param('folderPath') folderPath: string): Promise<FilePickerCreateFolderResponseDto> {
        const result = await this.filePickerService.createFolder({ folderPath });

        return {
            folderId: result.folderId,
            path: result.path,
        };
    }

    @Delete('/item/:itemPath')
    @HttpCode(204)
    @ApiNoContentResponse()
    public async deleteItem(@Param('itemPath') itemPath: string): Promise<void> {
        await this.filePickerService.deleteFileOrFolder(itemPath);
    }

    @Get('/search')
    @ApiOkResponse({ type: FilePickerSearchResponseDto })
    public async search(@Query() query: FilePickerSearchQueryDto): Promise<FilePickerSearchResponseDto> {
        return this.filePickerService.getSearchResults(query);
    }

    @Get('/recent')
    @ApiOkResponse({ type: FilePickerRecentFileResponseDto })
    public async getRecent(): Promise<FilePickerRecentFileResponseDto> {
        const items = await this.filePickerService.getRecentFiles();

        return {
            items,
        };
    }

    @Get('/folder-breadcrumb')
    @ApiOkResponse({ type: FilePickerFolderBreadcrumbResponseDto })
    public async getFolderBreadcrumb(
        @Query() query: FilePickerFolderBreadcrumbQueryDto
    ): Promise<FilePickerFolderBreadcrumbResponseDto> {
        const items = await this.filePickerService.getEgBreadcrumb(query);

        return {
            items,
        };
    }
}
