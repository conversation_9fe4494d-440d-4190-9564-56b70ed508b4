import { SearchV2SummaryReqBody, SortByEnum, SortDirectionEnum } from '@integrations/egnyte-ts-sdk';

export class EgnyteSearchRequestModel {
    public static toApiRequest(model: EgnyteSearchRequestModel): SearchV2SummaryReqBody {
        return {
            query: model.query,
            type: model.type,
            snippet_requested: model.snippetRequested,
            modified_before: model.modifiedBefore,
            modified_after: model.modifiedAfter,
            uploaded_before: model.uploadedBefore,
            uploaded_after: model.uploadedAfter,
            sort_by: model.sortBy,
            sort_direction: model.sortDirection,
            count: model.count,
            offset: model.offset,
        };
    }

    public type?: 'FILE' | 'FOLDER';

    public snippetRequested?: boolean;

    public query?: string;

    public modifiedBefore?: number;

    public modifiedAfter?: number;

    public uploadedBefore?: number;

    public uploadedAfter?: number;

    public sortBy?: SortByEnum;

    public sortDirection?: SortDirectionEnum;

    public count?: number;

    public offset?: number;
}
