import { IsBoolean, IsOptional, IsString } from '@nestjs/class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TransformBooleanStringToBoolean } from 'ms-metaos-decorators';

export class StartQueryDto {
    @ApiProperty()
    @IsString()
    public setupId: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    @TransformBooleanStringToBoolean()
    public updateAdminToken?: boolean;

    @ApiProperty()
    @IsBoolean()
    @TransformBooleanStringToBoolean()
    public multidomainContext: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public multidomainContextDomain?: string;
}
