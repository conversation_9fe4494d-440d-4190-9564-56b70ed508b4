import { UploadLinkExpirationNestedDto } from '../dtos/upload-link-expiration-nested.dto';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { Mappers } from '../../common/utils/mappers';
import { SHARE_LINK_EXPIRATION_UNIT } from '../../share-link/enums/share-link-expiration-unit.enum';

export class UploadLinkCreateExpirationModel {
    @ValidateArgs()
    public static fromDto(data: UploadLinkExpirationNestedDto): UploadLinkCreateExpirationModel {
        return {
            isActive: data.isActive,
            ...(data.isActive && { unitType: Mappers.mapRawToModelExpirationUnitType(data.unitType) }),
            ...(data.isActive && { value: data.value }),
        };
    }

    public isActive: boolean;

    public unitType?: SHARE_LINK_EXPIRATION_UNIT;

    public value?: number;
}
