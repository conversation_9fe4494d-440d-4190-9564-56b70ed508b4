import { IsBoolean, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UploadLinkItemNestedDto {
    @ApiProperty({ type: String })
    @IsNotEmpty()
    @IsString()
    public path: string;

    @ApiProperty({ type: Boolean, required: false })
    @IsOptional()
    @IsBoolean()
    public folderPerRecipient?: boolean;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    public id: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsBoolean()
    public isFolder: true;
}
