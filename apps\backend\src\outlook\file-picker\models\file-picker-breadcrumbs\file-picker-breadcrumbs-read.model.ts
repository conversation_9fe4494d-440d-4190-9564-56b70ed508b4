import { FilePickerItemReadModel } from '../file-picker-item/file-picker-item-read.model';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class FilePickerBreadcrumbsReadModel {
    @ValidateArgs()
    public static fromData(
        folderId: string,
        path: string,
        items: FilePickerItemReadModel[]
    ): FilePickerBreadcrumbsReadModel {
        return {
            folderId,
            path,
            items,
        };
    }

    public folderId: string;

    public path: string;

    public items: FilePickerItemReadModel[];
}
