import { Module } from '@nestjs/common';
import { DatabasePendingExternalUserModule } from 'ms-metaos-database/teams';
import { ExternalUsersController } from './controllers/external-users.controller';
import { ExternalUsersService } from './services/external-users.service';

@Module({
    imports: [DatabasePendingExternalUserModule],
    controllers: [ExternalUsersController],
    providers: [ExternalUsersService],
})
export class ExternalUsersModule {}
