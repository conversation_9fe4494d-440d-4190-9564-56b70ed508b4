import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PinoLogger } from 'nestjs-pino';
import { MSAuth } from '@integrations/pint-ms';
import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import { inspect } from 'util';
import { AuthSetupRepository } from 'ms-metaos-database/outlook';
import { AuthMSReadModel } from '../../../auth/models/auth-ms/auth-ms-read.model';
import { AuthMSSSOModel } from '../../../auth/models/auth-ms/auth-ms-sso.model';
import { AuthMSCodeModel } from '../../../auth/models/auth-ms/auth-ms-code.model';
import { DEFAULT_TENANT_ID } from '../../consts/ms-auth.const';

@Injectable()
export class MsAuthService {
    private readonly msAuthInstance: MSAuth;

    private readonly scopes: string[];

    constructor(
        private readonly configService: ConfigService,
        private readonly logger: PinoLogger,
        private readonly authSetupRepository: AuthSetupRepository
    ) {
        logger.setContext(MsAuthService.name);

        this.msAuthInstance = new MSAuth({
            authority: configService.get('msAuthority'),
            clientId: configService.get('msClientId'),
            clientSecret: configService.get('msClientSecret'),
        });

        this.scopes = configService.get('msScopes');
    }

    public getRedirectToMsAuthorize({
        state,
        loginHint,
        tenantId = DEFAULT_TENANT_ID,
        redirectUri,
        prompt = 'none',
    }: {
        state: string;
        loginHint?: string;
        tenantId?: string;
        redirectUri: string;
        prompt?: string;
    }): string {
        const authCodeUrl = this.msAuthInstance.getAuthCodeUrl({
            redirectUri,
            scopes: this.scopes,
            state,
            tenantId,
            loginHint,
            prompt,
        });

        return authCodeUrl.redirectUri;
    }

    public getInitialAuthData() {
        return {
            clientId: this.configService.get('msClientId'),
            scopes: this.configService.get('msScopes'),
        };
    }

    public getRedirectToConsent({
        redirectUri,
        tenantId = DEFAULT_TENANT_ID,
    }: {
        redirectUri: string;
        tenantId?: string;
    }): string {
        const adminConsentUrl = this.msAuthInstance.getAdminConsentUrl({
            redirectUri,
            scopes: this.scopes,
            tenantId,
        });

        return adminConsentUrl.redirectUri;
    }

    public async handleOnBehalfOf(authMSSSO: AuthMSSSOModel): Promise<AuthMSReadModel> {
        const response = await this.msAuthInstance.getTokenOnBehalfOf({
            assertion: authMSSSO.assertion,
            scopes: this.scopes,
            tenantId: authMSSSO.tenantId,
        });

        return AuthMSReadModel.fromResponse(response);
    }

    public async handleAuthCode(authMSCode: AuthMSCodeModel): Promise<AuthMSReadModel> {
        const response = await this.msAuthInstance.getTokenByCode({
            code: authMSCode.code,
            redirectUri: authMSCode.redirectUri,
            scopes: this.scopes,
            tenantId: authMSCode.tenantId,
        });

        return AuthMSReadModel.fromResponse(response);
    }

    public async getValidToken(options: {
        accessToken: string;
        refreshToken: string;
        expireTime: number;
        scopes: string[];
        tenantId?: string;
        refreshThreshold?: number;
        persistDataCallback?: (opts: IGetTokenResponse) => unknown;
    }): Promise<IGetTokenResponse> {
        return this.msAuthInstance.getValidToken(options);
    }

    public async isUserEmailValid(accessToken: string, email: string, userId: string): Promise<boolean> {
        const isValidationEnabled = this.configService.get<boolean>('enableMsEmailValidation');

        if (!isValidationEnabled) {
            this.logger.info(`User email validation skipped, config value: ${isValidationEnabled}`);

            return;
        }

        let validationResult: boolean;
        try {
            const response = await this.msAuthInstance.validateUserEmail({
                accessToken,
                emailToValidate: email,
            });
            validationResult = response.isValid;

            this.logger.info(`User email validation result: ${inspect(validationResult)}`);
        } catch (e) {
            this.logger.error(`User email validation error: ${e.message ?? 'Unknown'}`);

            throw new Error('E-mail validation error');
        }

        if (!validationResult) {
            try {
                await this.authSetupRepository.removeAuthSetup(userId);
            } catch (e) {
                this.logger.error(`User email validation - remove db data error: ${inspect(e)}`);
            }

            throw new Error('E-mail address assigned to the mailbox is different');
        }
    }
}
