import { ApiProperty } from '@nestjs/swagger';
import { SettingsReadUploadFolderModel } from 'ms-metaos-database/outlook';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class SettingsGetEgUploadFolderResponseNestedDto {
    @ValidateArgs()
    public static fromResponse(data: SettingsReadUploadFolderModel): SettingsGetEgUploadFolderResponseNestedDto {
        return {
            folderId: data.folderId,
            isFolder: data.isFolder,
            name: data.name,
            path: data.path,
        };
    }

    @ApiProperty()
    public folderId: string;

    @ApiProperty()
    public path: string;

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public isFolder: boolean;
}
