import { BadRequestException, Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON>eader, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { PinoLogger } from 'nestjs-pino';
import { inspect } from 'util';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { FilePickerService } from '../services/file-picker.service';
import { FilePickerFolderInfoResponseDto } from '../dtos/file-picker-folder-info-response.dto';
import { FilePickerFolderBreadcrumbsResponseDto } from '../dtos/file-picker-folder-breadcrumbs-response.dto';
import { IgnoreLogging, IgnoreLoggingOptions } from '../../common/decorators/ignore-logging.decorator';
import { FilePickerFolderInfoQueryDto } from '../dtos/file-picker-folder-info-query.dto';
import { FilePickerInfoOptionsModel } from '../models/file-picker-item/file-picker-info-options.model';
import { FilePickerSearchQueryDto } from '../dtos/file-picker-search-query.dto';

@ApiTags('FilePickerController')
@ApiHeader({
    name: 'Authorization',
    description: 'Contains JWT token',
})
@Controller('outlook/file-picker')
@IgnoreLogging(IgnoreLoggingOptions.START_STOP)
@UseGuards(JwtGuard)
export class FilePickerController {
    constructor(
        private readonly filePickerService: FilePickerService,
        readonly logger: PinoLogger
    ) {}

    @Get('bookmarks')
    @ApiOkResponse({
        type: FilePickerFolderInfoResponseDto,
    })
    async getBookmarks(): Promise<FilePickerFolderInfoResponseDto> {
        const response = await this.filePickerService.getBookmarkedItems();

        return FilePickerFolderInfoResponseDto.fromBookmarksResponse(response);
    }

    @Get('search')
    @ApiOkResponse({
        type: FilePickerFolderInfoResponseDto,
    })
    async getItemsBySearchString(
        @Query() searchQuery: FilePickerSearchQueryDto
    ): Promise<FilePickerFolderInfoResponseDto> {
        const response = await this.filePickerService.searchForItems(searchQuery);

        return FilePickerFolderInfoResponseDto.fromSearchResultsResponse(response);
    }

    @Get('recents')
    @ApiOkResponse({
        type: FilePickerFolderInfoResponseDto,
    })
    async getRecentFiles(): Promise<FilePickerFolderInfoResponseDto> {
        const response = await this.filePickerService.getRecentItems();

        return FilePickerFolderInfoResponseDto.fromRecentsResponse(response);
    }

    @Get(':id/breadcrumbs')
    @ApiOkResponse({
        type: FilePickerFolderBreadcrumbsResponseDto,
    })
    public async getFolderBreadcrumbs(@Param('id') id: string): Promise<FilePickerFolderBreadcrumbsResponseDto> {
        const response = await this.filePickerService.getBreadcrumbsByFolderId(id);

        return FilePickerFolderBreadcrumbsResponseDto.fromResponse(response);
    }

    @Get()
    @ApiOkResponse({
        type: FilePickerFolderInfoResponseDto,
    })
    async metaData(@Query() queryData: FilePickerFolderInfoQueryDto): Promise<FilePickerFolderInfoResponseDto> {
        let result;
        const options = FilePickerInfoOptionsModel.fromDto(queryData);
        if ('id' in queryData) {
            result = await this.filePickerService.getDataByFolderId(queryData.id, options);
        } else if ('path' in queryData) {
            result = await this.filePickerService.getDataByFolderPath(queryData.path, options);
        } else {
            this.logger.warn(`File picker bad request query, ${inspect(queryData)}`);
            throw new BadRequestException(`At least id or path must be provided`);
        }

        return FilePickerFolderInfoResponseDto.fromFolderInfoResponse(result);
    }
}
