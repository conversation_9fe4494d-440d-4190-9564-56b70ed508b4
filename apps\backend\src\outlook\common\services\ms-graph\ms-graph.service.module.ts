import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseAuthModule } from 'ms-metaos-database/outlook';
import { MsAuthServiceModule } from '../ms-auth/ms-auth.service.module';
import { MsGraphService } from './ms-graph.service';
import { CredentialsProvider } from '../credentials-provider/credentials-provider';

@Module({
    imports: [MsAuthServiceModule, DatabaseAuthModule],
    providers: [CredentialsProvider, MsGraphService],
    exports: [MsGraphService],
})
export class MsGraphServiceModule {}
