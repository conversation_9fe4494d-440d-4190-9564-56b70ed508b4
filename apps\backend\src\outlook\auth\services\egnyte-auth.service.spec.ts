import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { Pi<PERSON>Logger } from 'nestjs-pino';
import { of } from 'rxjs';
import { EgnyteAuthService } from './egnyte-auth.service';
import { getConfigServiceMock } from '../../common/config/configuration.mock';
import { DomainEnvMetadata } from '../../common/utils/domain-env-metadata';
import { getDomainEnvMetadataMock } from '../../common/utils/domain-env-metadata.mock';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { getEgnyteClientServiceMock } from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { Helpers } from '../../common/utils/helpers';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getEgUserInfoMock } from './egnyte-auth.service.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { getAuthEGCreateModelMock } from '../models/auth-eg/auth-eg-create.mock';
import { getAuthEGReadModelMock } from '../models/auth-eg/auth-eg-read.mock';
import { getUserDetailsModelMock } from '../../common/models/user-details/user-details.mock';

describe('EgnyteAuthService', () => {
    const accessTokenResponseMock = {
        data: {
            access_token: UNIT_TEST_VARS.egAccessToken,
        },
    };

    let configService: ConfigService;
    let logger: PinoLogger;
    let egnyteClientService: EgnyteClientService;

    let egnyteAuthService: EgnyteAuthService;

    beforeEach(async () => {
        jest.resetAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: ConfigService,
                    useValue: getConfigServiceMock(),
                },
                {
                    provide: DomainEnvMetadata,
                    useValue: getDomainEnvMetadataMock(),
                },
                {
                    provide: HttpService,
                    useValue: {
                        post: jest.fn().mockReturnValue(of(Promise.resolve(accessTokenResponseMock))),
                        get: jest.fn().mockReturnValue(of(Promise.resolve({ data: getEgUserInfoMock() }))),
                    },
                },
                {
                    provide: EgnyteClientService,
                    useValue: getEgnyteClientServiceMock(),
                },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                EgnyteAuthService,
            ],
        }).compile();

        configService = module.get<ConfigService>(ConfigService);
        logger = module.get<PinoLogger>(PinoLogger);
        egnyteClientService = module.get<EgnyteClientService>(EgnyteClientService);
        egnyteAuthService = module.get<EgnyteAuthService>(EgnyteAuthService);
    });

    describe('constructor', () => {
        it('should successfully setup logger context', () => {
            expect(logger.setContext).toHaveBeenCalledWith(EgnyteAuthService.name);
        });

        it.each([{ key: 'definitionApiKey' }, { key: 'masheryAppSecret' }, { key: 'tokenScope' }])(
            'should successfully get `$key` from configuration',
            (args: { key: string }) => {
                expect(configService.get).toHaveBeenCalledWith(args.key);
            }
        );
    });

    describe('getAuthUrl', () => {
        it('should successfully return valid authUrl with domain', () => {
            const queryString = Helpers.buildQueryString({
                client_id: configService.get('definitionApiKey'),
                redirect_uri: UNIT_TEST_VARS.redirectUri,
                state: encodeURIComponent(UNIT_TEST_VARS.userId),
                include_domain: 'true',
                response_type: 'code',
                scope: configService.get('tokenScope'),
                domain: UNIT_TEST_VARS.egDomain,
            });
            const authBaseUrl = configService.get('egAuthBaseUrl');
            const expected = `${authBaseUrl}?${queryString}`;

            const result = egnyteAuthService.getAuthUrl(
                UNIT_TEST_VARS.userId,
                UNIT_TEST_VARS.redirectUri,
                UNIT_TEST_VARS.egDomain
            );

            expect(result).toBe(expected);
        });

        it('should successfully return valid authUrl without domain', () => {
            const queryString = Helpers.buildQueryString({
                client_id: configService.get('definitionApiKey'),
                redirect_uri: UNIT_TEST_VARS.redirectUri,
                state: encodeURIComponent(UNIT_TEST_VARS.userId),
                include_domain: 'true',
                response_type: 'code',
                scope: configService.get('tokenScope'),
            });
            const authBaseUrl = configService.get('egAuthBaseUrl');
            const expected = `${authBaseUrl}?${queryString}`;

            const result = egnyteAuthService.getAuthUrl(UNIT_TEST_VARS.userId, UNIT_TEST_VARS.redirectUri);

            expect(result).toBe(expected);
        });

        it('should throw an error, because there is no userId', () => {
            const expectedError = new Error('Session id is missing in request');

            expect(() => egnyteAuthService.getAuthUrl(undefined, UNIT_TEST_VARS.redirectUri)).toThrow(expectedError);
        });
    });

    describe('handleAuthCode', () => {
        it('should successfully return authCode response', async () => {
            const result = await egnyteAuthService.handleAuthCode(getAuthEGCreateModelMock());

            const expected = getAuthEGReadModelMock();

            expect(logger.info).toHaveBeenCalled();
            expect(result).toEqual(expected);
        });

        it('should throw an error, because there is error in query', async () => {
            const expectedError = new ForbiddenException('Access was denied. Please try again');

            await expect(
                egnyteAuthService.handleAuthCode(getAuthEGCreateModelMock({ withoutError: false }))
            ).rejects.toThrow(expectedError);
            expect(logger.error).toHaveBeenCalled();
        });

        it('should throw an error, because there is no code property found', async () => {
            const expectedError = new BadRequestException('Code not provided!');
            const authEGCreateModelMock = getAuthEGCreateModelMock({ withoutCode: true });

            await expect(egnyteAuthService.handleAuthCode(authEGCreateModelMock)).rejects.toThrow(expectedError);
            expect(logger.error).toHaveBeenCalled();
        });

        it('should throw an error, because there is not found domain property', async () => {
            const expectedError = new BadRequestException('Domain not provided!');
            await expect(
                egnyteAuthService.handleAuthCode(getAuthEGCreateModelMock({ withoutDomain: true }))
            ).rejects.toThrow(expectedError);
            expect(logger.error).toHaveBeenCalled();
        });
    });

    describe('getEgUserInfo', () => {
        it('should successfully return userInfo data', async () => {
            const result = await egnyteAuthService.getEgUserInfo();
            const expected = getUserDetailsModelMock();

            expect(result).toEqual(expected);
            expect(egnyteClientService.getUserInfo).toHaveBeenCalledTimes(1);
        });
    });
});
