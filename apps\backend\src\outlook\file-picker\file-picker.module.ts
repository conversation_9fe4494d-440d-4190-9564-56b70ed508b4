import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { FilePickerController } from './controllers/file-picker.controller';
import { FilePickerService } from './services/file-picker.service';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';
import { MsGraphServiceModule } from '../common/services/ms-graph/ms-graph.service.module';

@Module({
    imports: [AuthModule, EgnyteClientServiceModule, MsGraphServiceModule],
    controllers: [FilePickerController],
    providers: [FilePickerService],
})
export class FilePickerModule {}
