import { ApiProperty } from '@nestjs/swagger';
import { UserEnum } from '@integrations/egnyte-ts-sdk';

class EgBranding {
    @ApiProperty()
    public logo: string;

    @ApiProperty()
    public color: string;

    @ApiProperty()
    public label: string;
}

class EgUser {
    @ApiProperty()
    public usernameFormatted: string;

    @ApiProperty({ type: String, enum: UserEnum, example: UserEnum.ADMIN })
    public userType: UserEnum;

    @ApiProperty()
    public userId: string;

    @ApiProperty()
    public username: string;

    @ApiProperty()
    public email: string;
}

export class UserDetailsResponseDto {
    @ApiProperty({ type: EgBranding })
    public egBranding: EgBranding;

    @ApiProperty()
    public egDomain: string;

    @ApiProperty({ type: EgUser })
    public egUser: EgUser;

    @ApiProperty()
    public isMainAdmin: boolean;
}
