import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { ApiCreatedResponse, ApiHeader, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { ShareLinkService } from '../services/share-link.service';
import { ShareLinkRequestDto } from '../dtos/share-link-request.dto';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { Auth } from '../../common/decorators/auth.decorator';
import { ShareLinkResponseDto } from '../dtos/share-link-response.dto';
import { ShareLinkRecipientsResponseDto } from '../dtos/share-link-recipients-response.dto';
import { ShareLinkPermissionsResponseDto } from '../dtos/share-link-permissions-response.dto';
import { ShareLinkPermissionsQueryDto } from '../dtos/share-link-permissions-query.dto';
import { ShareLinkCreateModel } from '../models/share-link/share-link-create.model';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { ReportingActions } from '../../common/services/reporting/reporting.enums';

@ApiTags('ShareLinkController')
@ApiHeader({
    name: 'Authorization',
    description: 'Contains JWT token',
})
@Controller('outlook/share-link')
@UseGuards(JwtGuard)
export class ShareLinkController {
    constructor(
        private readonly shareLinkService: ShareLinkService,
        private readonly reportingService: ReportingService,
        private readonly logger: PinoLogger
    ) {}

    @ApiCreatedResponse({
        type: ShareLinkResponseDto,
        isArray: true,
    })
    @Post()
    public async createShareLink(
        @Auth() auth: AuthReadModel,
        @Body() shareLinkDto: ShareLinkRequestDto
    ): Promise<ShareLinkResponseDto[]> {
        const shareLinkCreateModel = ShareLinkCreateModel.fromDto(shareLinkDto);
        const result = await this.shareLinkService.createShareLinkByFileId(shareLinkCreateModel);

        result.forEach((shareLink, index) => {
            this.reportingService.processEvent({
                action: ReportingActions.SHARE_LINK,
                domain: auth.egnyte.domain,
                username: auth.egnyte.username,
                userId: auth.egnyte.id,
                fileId: shareLinkCreateModel.items[index].id,
                tags: `${ReportingActions.SHARE_LINK}-${shareLinkCreateModel.shareType},${shareLink.type}`,
                statusCode: 201,
            });
        });

        return result.map((singleLink) => ShareLinkResponseDto.fromResponse(singleLink));
    }

    @ApiOkResponse({
        type: ShareLinkRecipientsResponseDto,
    })
    @Get('recipients')
    async searchRecipients(@Query('q') queryString: string): Promise<ShareLinkRecipientsResponseDto> {
        const egUsersInfo = await this.shareLinkService.searchForRecipients(queryString);

        this.logger.info(`Recipients found: ${egUsersInfo.items.map((item) => item.email).join(', ')}`);

        return ShareLinkRecipientsResponseDto.fromResponse(egUsersInfo);
    }

    @ApiOkResponse({
        type: ShareLinkPermissionsResponseDto,
    })
    @Get('permissions')
    public async getPermissions(
        @Query() queryParams: ShareLinkPermissionsQueryDto
    ): Promise<ShareLinkPermissionsResponseDto> {
        const result = await this.shareLinkService.getPermissions({
            ...('folderId' in queryParams && { folderId: queryParams.folderId }),
            ...('fileId' in queryParams && { fileId: queryParams.fileId }),
        });

        const shareLinkPermissionsResponse = ShareLinkPermissionsResponseDto.fromResponse(result);

        this.logger.info(
            `Permissions found. Item id: ${
                queryParams.folderId || queryParams.fileId
            }. Permissions response: ${JSON.stringify(shareLinkPermissionsResponse)}`
        );

        return shareLinkPermissionsResponse;
    }
}
