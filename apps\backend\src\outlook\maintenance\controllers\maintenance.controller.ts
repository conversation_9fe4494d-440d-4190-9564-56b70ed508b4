import { <PERSON>, Get, Query, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import * as Prometheus from '@integrations/pint-prometheus';
import { JwtService } from '@nestjs/jwt';
import { AuthRepository, SettingsRepository } from 'ms-metaos-database/outlook';

@Controller('outlook')
export class MaintenanceController {
    constructor(
        private readonly authRepository: AuthRepository,
        private readonly settingsRepository: SettingsRepository,
        private readonly jwtService: JwtService
    ) {}

    @Get('metrics')
    async getMetrics(@Req() req: Request, @Res() res: Response): Promise<(req: Request, res: Response) => void> {
        const metricsEndpoint = Prometheus.getMetricsEndpoint();

        return metricsEndpoint(req, res);
    }

    @Get('migrate-auth')
    async migrateAuth(@Query('limit') limit): Promise<{ success: string[]; failed: string[] }> {
        const authLimit = limit || 50;
        const auths = await this.authRepository.findWithCustomQuery(
            { 'microsoft.amUrl': { $not: /MIGRATED/ } },
            authLimit
        );
        const status = {
            success: [],
            failed: [],
        };

        await Promise.all(
            auths.map(async (auth) => {
                const decodedToken = this.jwtService.decode(auth.microsoft.accessToken);
                const newUserId = Buffer.from(`${decodedToken.tid}_${decodedToken.oid}`).toString('base64');

                try {
                    await this.authRepository.migrateUserId(auth.userId, newUserId, 'MIGRATED');
                    await this.settingsRepository.migrateUserId(auth.userId, newUserId);
                    status.success.push(newUserId);
                } catch (e) {
                    await this.authRepository.migrateUserId(newUserId, auth.userId, auth.microsoft.amUrl);
                    status.failed.push(auth.userId);
                }
            })
        );

        return status;
    }
}
