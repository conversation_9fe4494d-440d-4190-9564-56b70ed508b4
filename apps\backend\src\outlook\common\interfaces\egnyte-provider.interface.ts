/* eslint-disable camelcase */
import { IncomingMessage } from 'http';

export enum EGNYTE_LINK_TYPE {
    ANYONE = 'anyone',
    PASSWORD = 'password',
    DOMAIN = 'domain',
    DIRECT = 'direct',
    RECIPIENTS = 'recipients',
}

export enum EGNYTE_PERMISSION {
    VIEWER = 'Viewer',
    EDITOR = 'Editor',
    FULL = 'Full',
    OWNER = 'Owner',
}

export enum EGNYTE_USER_TYPE {
    ADMIN = 'admin',
    POWER = 'power',
    STANDARD = 'standard',
}

export enum EGNYTE_USER_AUTH_TYPE {
    AD = 'ad',
    SSO = 'sso',
    EGNYTE = 'egnyte',
}

export enum EGNYTE_PUBLIC_LINKS_ALLOWED_FOR {
    FILES_FOLDERS = 'files_folders',
    FILES = 'files',
    DISABLED = 'disabled',
}

export enum EGNYTE_LINK_PROTECTION {
    NONE = 'NONE',
    PREVIEW = 'PREVIEW',
}

export enum EGNYTE_RESTRICTION_TYPE {
    WARN = 'WARN',
    BLOCK = 'BLOCK',
}

export type EgnyteLinkTimeExpirationDefaultValue = {
    protection: EGNYTE_LINK_PROTECTION;
    expiry_date: string;
};
export type EgnyteLinkClickExpirationDefaultValue = {
    protection: EGNYTE_LINK_PROTECTION;
    expiry_clicks: number;
};
export type EgnyteLinkExpirationDefaultValue =
    | EgnyteLinkTimeExpirationDefaultValue
    | EgnyteLinkClickExpirationDefaultValue;

export const isEgnyteLinkClickExpirationDefaultValue = (
    defaultValue: EgnyteLinkExpirationDefaultValue
): defaultValue is EgnyteLinkClickExpirationDefaultValue => !!defaultValue && 'expiry_clicks' in defaultValue;

export const isEgnyteLinkTimeExpirationDefaultValue = (
    defaultValue: EgnyteLinkExpirationDefaultValue
): defaultValue is EgnyteLinkTimeExpirationDefaultValue => !!defaultValue && 'expiry_date' in defaultValue;

export type EgnyteLinkTimeExpirationRestrictions = {
    [key in EGNYTE_RESTRICTION_TYPE]?: {
        explanations: EGNYTE_RESTRICTION_EXPLANATIONS;
        applyAbove: string;
    };
};
export type EgnyteLinkClickExpirationRestrictions = {
    [key in EGNYTE_RESTRICTION_TYPE]?: {
        explanations: EGNYTE_RESTRICTION_EXPLANATIONS;
        applyAbove: number;
    };
};

export type EgnyteLinkExpirationRestrictions = {
    protection: {
        [key in EGNYTE_LINK_PROTECTION]?: EGNYTE_RESTRICTION;
    };
    expiry_clicks: EgnyteLinkClickExpirationRestrictions;
    expiry_date: EgnyteLinkTimeExpirationRestrictions;
};

// list_content
export interface IEgnyteListContents<T = Record<string, unknown>> {
    count: number;
    offset: number;
    total_count: number;
    folders: IEgnyteSdkItem<T>[];
    files: IEgnyteSdkItem<T>[];
}

// perms
export interface IEgnytePerms {
    permissions: {
        users: Array<{
            subject: string;
            permission: EGNYTE_PERMISSION;
        }>;
        groups: Array<{
            subject: string;
            permission: EGNYTE_PERMISSION;
        }>;
    };
}

// allowed_link_types
export interface IEgnyteAllowedLinkTypes {
    allow_upload_links: boolean;
    allowed_file_link_types: EGNYTE_LINK_TYPE[];
    allowed_folder_link_types: EGNYTE_LINK_TYPE[];
}

// default_attributes
export interface IEgnyteDefaultAttributes {
    accessibility: Exclude<EGNYTE_LINK_TYPE, EGNYTE_LINK_TYPE.DIRECT>;
    publicLinks?: EgnyteLinkExpirationDefaultValue;
    privateLinks: EgnyteLinkExpirationDefaultValue;
}

export type EGNYTE_RESTRICTION_EXPLANATIONS = {
    restrictionId: string;
    message: string;
}[];

export type EGNYTE_RESTRICTION = {
    type: EGNYTE_RESTRICTION_TYPE;
    explanations: EGNYTE_RESTRICTION_EXPLANATIONS;
};

// sharing_restrictions
export interface IEgnyteSharingRestrictions {
    accessibility: {
        [key in Exclude<EGNYTE_LINK_TYPE, EGNYTE_LINK_TYPE.DIRECT>]?: EGNYTE_RESTRICTION;
    };
    publicLinks: EgnyteLinkExpirationRestrictions;
    privateLinks: EgnyteLinkExpirationRestrictions;
}

// include_perm
export interface IEgnytePermission {
    permission: EGNYTE_PERMISSION;
}

// list_custom_metadata
export interface IEgnyteCustomMetadata {
    custom_metadata: Array<{ [key: string]: Record<string, string> }>;
}

// ------------------------
export type IEgnyteSdkResult<T = IEgnyteListContents> = {
    is_folder: boolean;
    folder_id: string;
    parent_id: string;
    name: string;
    path: string;
    public_links: EGNYTE_PUBLIC_LINKS_ALLOWED_FOR;
    allow_links: boolean;
    restrict_move_delete: boolean;
    uploaded: number;
    lastModified: number;
} & T;

// ------------------------
export type IEgnyteSdkItem<T = Record<string, unknown>> = {
    is_folder: boolean;
    parent_id: string;
    name: string;
    path: string;
    uploaded: number;
    // file props
    last_modified?: string;
    entry_id?: string;
    group_id?: string;
    uploaded_by?: string;
    checksum?: string;
    num_versions?: number;
    locked?: boolean;
    size?: number;
    // folder props
    folder_id?: string;
    lastModified?: number;
} & T;

export interface IEgnyteUserInfo {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    user_type: EGNYTE_USER_TYPE;
}

export interface IEgnyteSearchResult {
    results: IEgnyteSdkItem[];
    offset: number;
    count: number;
    hasMore: boolean;
    total_count: number;
}

export interface IEgnyteSearchUsersResult {
    startIndex: number;
    totalResults: number;
    itemsPerPage: number;
    resources: IEgnyteSearchUserResult[];
}

export interface IEgnyteSearchUserResult {
    id: number;
    userName: string;
    externalId: string;
    email: string;
    name: {
        formatted: string;
        familyName: string;
        givenName: string;
    };
    active: boolean;
    locked: boolean;
    emailChangePending: boolean;
    authType: EGNYTE_USER_AUTH_TYPE;
    userType: EGNYTE_USER_TYPE;
    role: string | null;
    idpUserId: string;
    userPrincipalName: string | null;
    expiryDate: number | null;
    deleteOnExpiry: number | null;
    createdDate: string;
    lastModificationDate: string;
    lastActiveDate: string;
    isServiceAccount: boolean;
}

export interface IEgnyteSdkPromiseRequestBookmarks {
    offset: number;
    count: number;
    bookmarks: IEgnyteSdkItem[];
}

export interface IEgnyteSdkPromiseRequestRecentFiles {
    offset: number;
    count: number;
    recentFiles: IEgnyteSdkItem[];
}

export interface IEgnyteSdkMetadataNamespaceRead {
    name: string;
    scope: 'public' | 'private';
    keys: {
        [key: string]: Pick<IEgnyteSdkMetadataKey, 'type' | 'displayName'>;
    };
    displayName: string;
    priority: number;
    editable: boolean;
}

export interface IEgnyteSdkMetadataNamespaceWrite {
    name: string;
    scope: 'public' | 'private' | 'protected';
    keys: {
        [key: string]: IEgnyteSdkMetadataKey;
    };
    displayName?: string;
    priority?: number;
}

export type IEgnyteSdkMetadataKey = {
    type: 'integer' | 'string' | 'decimal' | 'date' | 'enum';
    data?: string[];
    displayName?: string;
    priority?: number;
    helpText?: string;
};

interface IEgnyteInstanceStorage {
    path: (...args: unknown[]) => {
        get: (...args: unknown[]) => Promise<IEgnyteSdkResult>;
        storeFile(file: NodeJS.ReadableStream): Promise<IEgnyteSdkItem>;
        remove: (...args: unknown[]) => Promise<void>;
    };
    folderId: (...args: unknown[]) => { get: (...args: unknown[]) => Promise<IEgnyteSdkResult> };
}

interface IEgnyteInstanceAuth {
    getUserInfo: () => Promise<IEgnyteUserInfo>;
}

interface IEgnyteInstanceManual {
    promiseRequest: <T = unknown>(
        args: Record<string, unknown>
    ) => Promise<{
        response: IncomingMessage;
        body: T;
    }>;
}

interface IEgnyteInstanceSearch {
    query: (searchString: string, page: number) => Promise<IEgnyteSearchResult>;
}

type IEgnyteTraceId<T> = T & {
    requestId: (traceId: string) => T;
};

export interface IEgnyteInstance {
    domain: string;
    API: {
        storage: IEgnyteTraceId<IEgnyteInstanceStorage>;
        auth: IEgnyteInstanceAuth;
        manual: IEgnyteInstanceManual;
        search: IEgnyteInstanceSearch;
    };
}

export interface IEgnyteUploadLinkRequest {
    path: string;
    expiryDate?: string;
    folderPerRecipient?: boolean;
}

export interface IEgnyteUploadLinkResponse {
    links: IEgnyteUploadLinkItemResponse[];
    path: string;
    type: string;
}

export interface IEgnyteUploadLinkItemResponse {
    id: string;
    url: string;
}
