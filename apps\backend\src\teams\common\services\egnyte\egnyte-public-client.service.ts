import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { PinoLogger } from 'nestjs-pino';
import { EgnyteClient } from '@integrations/egnyte-ts-sdk';
import { AdminAuthRepository, AuthReadModel, AdminAuthReadModel } from 'ms-metaos-database/teams';
import { sanitizeDomain } from '../../utils/sanitize';

@Injectable()
export class EgnytePublicClientService {
    private static normalizeDomain(domain: string): string {
        if (domain.match(/^https:\/\//)) {
            return domain;
        }
        if (domain.match(/^[^./]+$/)) {
            return `https://${domain}.egnyte.com`;
        }

        return `https://${domain}`;
    }

    private static makeKey(normalizedDomain: string, token: string): string {
        return `${normalizedDomain}_${token}`;
    }

    // todo: improve types
    private instances: Record<string, any> = {};

    constructor(
        private clsService: ClsService,
        private adminAuthRepository: AdminAuthRepository,
        private logger: PinoLogger
    ) {
        // clear saved instances every 10 minutes
        setInterval(() => this.clearInstances(), 10 * 60 * 1000);
    }

    public async getUserInstance(): Promise<EgnyteClient> {
        const auth = this.getUserAuth();
        const normalizedDomain = EgnytePublicClientService.normalizeDomain(auth.egDomain);
        const key = EgnytePublicClientService.makeKey(normalizedDomain, auth.egAccessToken);

        if (!this.instances[key]) {
            this.instances[key] = EgnyteClient.init({
                domain: sanitizeDomain(normalizedDomain),
                token: auth.egAccessToken,
            });
        }

        return this.instances[key];
    }

    public getUserAuth(): AuthReadModel {
        if (!this.clsService.get('auth')) {
            this.logger.error(`[EgnyteClientService/getUserAuth] User not authorized`);
            throw new UnauthorizedException('User not authorized');
        }

        return this.clsService.get<AuthReadModel>('auth');
    }

    public async getAdminInstance(): Promise<EgnyteClient> {
        const adminAuth = await this.getAdminAuth();

        const key = EgnytePublicClientService.makeKey(adminAuth.egDomain, adminAuth.egAccessToken);

        if (!this.instances[key]) {
            this.instances[key] = EgnyteClient.init({
                domain: sanitizeDomain(adminAuth.egDomain),
                token: adminAuth.egAccessToken,
            });
        }

        return this.instances[key];
    }

    public async getAdminAuth(): Promise<AdminAuthReadModel> {
        if (!this.clsService.get('auth') && !this.clsService.get('adminAuth')) {
            this.logger.error(`[EgnyteClientService/getAdminAuth] User and admin not authorized`);
            throw new UnauthorizedException('User/admin not authorized');
        }

        if (this.clsService.get('adminAuth')) {
            const adminAuth = this.clsService.get<AdminAuthReadModel>('adminAuth');

            return adminAuth;
        }
        if (!this.clsService.get('auth')) {
            this.logger.error(`[EgnyteClientService/getAdminAuth] User not authorized`);
            throw new UnauthorizedException('User not authorized');
        }

        const auth = this.clsService.get<AuthReadModel>('auth');

        const adminAuth = await this.adminAuthRepository.getByTenantId(auth.msTenantId);

        if (!adminAuth) {
            this.logger.error(`[EgnyteClientService/getAdminAuth] Admin not authorized`);
            throw new UnauthorizedException('Admin not authorized');
        }

        return adminAuth;
    }

    public async getCustomInstance({ domain, token }: { domain: string; token: string }): Promise<EgnyteClient> {
        const normalizedDomain = EgnytePublicClientService.normalizeDomain(domain);

        const key = EgnytePublicClientService.makeKey(normalizedDomain, token);

        if (!this.instances[key]) {
            this.instances[key] = EgnyteClient.init({ domain: sanitizeDomain(domain), token });
        }

        return this.instances[key];
    }

    private clearInstances(): void {
        this.instances = {};
    }
}
