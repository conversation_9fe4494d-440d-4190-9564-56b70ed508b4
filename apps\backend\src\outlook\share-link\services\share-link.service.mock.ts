import { IPublic } from '../../common/utils/public.interface';
import { ShareLinkService } from './share-link.service';
import { getShareLinkUsersReadModelMock } from '../models/share-link-recipients/share-link-users-read.mock';
import { getShareLinkPermissionsReadModelMock } from '../models/share-link-permissions/share-link-permissions-read.mock';

export function getShareLinkServiceMock(): IPublic<ShareLinkService> {
    return {
        createShareLinkByFileId: jest.fn(),
        searchForRecipients: jest.fn().mockResolvedValue(getShareLinkUsersReadModelMock()),
        getPermissions: jest.fn().mockResolvedValue(getShareLinkPermissionsReadModelMock()),
    };
}
