import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { FilePickerItemReadModel } from '../models/file-picker-item/file-picker-item-read.model';

export class FilePickerFolderItemNestedDto {
    @ValidateArgs()
    public static fromResponse(data: FilePickerItemReadModel): FilePickerFolderItemNestedDto {
        return {
            name: data.name,
            path: data.path,
            isFolder: data.isFolder,
            ...(data.folderId && { folderId: data.folderId }),
            ...(data.fileId && { fileId: data.fileId }),
        };
    }

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public isFolder: boolean;

    @ApiProperty()
    public path: string;

    @ApiPropertyOptional()
    public folderId?: string;

    @ApiPropertyOptional()
    public fileId?: string;
}
