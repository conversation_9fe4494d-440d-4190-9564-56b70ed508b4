import { UserDetailsModel } from './user-details.model';
import { UNIT_TEST_VARS } from '../../utils/unit-test-vars';

export function getUserDetailsModelMock({ active = true, emailChangePending = false } = {}): UserDetailsModel {
    return {
        id: UNIT_TEST_VARS.egId,
        userType: UNIT_TEST_VARS.egUserType,
        username: UNIT_TEST_VARS.egUsername,
        domain: UNIT_TEST_VARS.egDomain,
        email: UNIT_TEST_VARS.egEmail,
        firstName: UNIT_TEST_VARS.egFirstName,
        lastName: UNIT_TEST_VARS.egLastName,
        active,
        emailChangePending,
    };
}
