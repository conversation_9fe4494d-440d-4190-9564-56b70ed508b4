import { ValidateIf, IsString, IsOptional } from '@nestjs/class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AuthCodeQueryDto {
    @ApiProperty()
    @ValidateIf((o) => !o.error)
    @IsString()
    code: string;

    @ApiProperty()
    @ValidateIf((o) => !o.error)
    @IsString()
    state: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    domain?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    error?: string;
}
