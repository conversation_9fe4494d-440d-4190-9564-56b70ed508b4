import { ApiProperty } from '@nestjs/swagger';
import { ShareLinkRecipientsNestedDto } from './share-link-recipients-nested.dto';
import { ShareLinkUsersReadModel } from '../models/share-link-recipients/share-link-users-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class ShareLinkRecipientsResponseDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkUsersReadModel): ShareLinkRecipientsResponseDto {
        return {
            recipients: data.items.map((item) => ShareLinkRecipientsNestedDto.fromResponse(item)),
        };
    }

    @ApiProperty({ type: ShareLinkRecipientsNestedDto, isArray: true })
    public recipients: ShareLinkRecipientsNestedDto[];
}
