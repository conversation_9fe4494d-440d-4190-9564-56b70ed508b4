import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { DatabaseAuthModule, DatabaseAuthSetupModule } from 'ms-metaos-database/outlook';
import { AppRelative } from '../common/utils/app-relative';
import { DomainEnvMetadata } from '../common/utils/domain-env-metadata';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { EgnyteAuthService } from './services/egnyte-auth.service';
import { IdentityStrategy } from './strategies/identity.strategy';
import { MsAuthServiceModule } from '../common/services/ms-auth/ms-auth.service.module';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';
import { MsAmUrlServiceModule } from '../common/services/ms-am-url/ms-am-url.service.module';

@Module({
    controllers: [AuthController],
    providers: [AuthService, DomainEnvMetadata, AppRelative, EgnyteAuthService, IdentityStrategy],
    imports: [
        MsAmUrlServiceModule,
        ConfigModule,
        HttpModule,
        PassportModule,
        JwtModule.registerAsync({
            useFactory: (configService: ConfigService) => ({
                secret: configService.get('jwtTokenSecret'),
                signOptions: {
                    expiresIn: configService.get('jwtTokenExpiration'),
                },
            }),
            inject: [ConfigService],
        }),
        MsAuthServiceModule,
        EgnyteClientServiceModule,
        DatabaseAuthModule,
        DatabaseAuthSetupModule,
    ],
    exports: [AuthService],
})
export class AuthModule {}
