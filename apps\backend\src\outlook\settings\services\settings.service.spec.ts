import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import {
    getSettingsReadModelMock,
    getSettingsRepositoryMock,
    getSettingsUpdateModelMock,
    SettingsRepository,
} from 'ms-metaos-database/outlook';
import { getReportingServiceMock } from '../../common/services/reporting/reporting.service.mock';
import { SettingsService } from './settings.service';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { getEgnyteClientServiceMock } from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getConfigServiceMock } from '../../common/config/configuration.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { getFilePickerItemReadModelMock } from '../../file-picker/models/file-picker-item/file-picker-item-read.mock';
import { ReportingService } from '../../common/services/reporting/reporting.service';

describe('SettingsService', () => {
    let testingModule: TestingModule;

    let settingsRepository: SettingsRepository;
    let egnyteClientService: EgnyteClientService;
    let logger: PinoLogger;
    let settingsService: SettingsService;
    let configService: ConfigService;

    beforeEach(async () => {
        jest.resetAllMocks();

        testingModule = await Test.createTestingModule({
            providers: [
                { provide: SettingsRepository, useValue: getSettingsRepositoryMock() },
                { provide: EgnyteClientService, useValue: getEgnyteClientServiceMock() },
                { provide: ReportingService, useValue: getReportingServiceMock() },
                { provide: PinoLogger, useValue: getLoggerMock() },
                { provide: ConfigService, useValue: getConfigServiceMock() },
                SettingsService,
            ],
        }).compile();

        settingsRepository = testingModule.get<SettingsRepository>(SettingsRepository);
        egnyteClientService = testingModule.get<EgnyteClientService>(EgnyteClientService);
        logger = testingModule.get<PinoLogger>(PinoLogger);
        settingsService = testingModule.get<SettingsService>(SettingsService);
        configService = testingModule.get<ConfigService>(ConfigService);
    });

    describe('getSettings', () => {
        it('should successfully get settings with all keys', async () => {
            (egnyteClientService.getFolderDetailsById as jest.Mock).mockResolvedValue(
                getFilePickerItemReadModelMock({
                    name: UNIT_TEST_VARS.settings.uploadFolder.name,
                    path: UNIT_TEST_VARS.settings.uploadFolder.path,
                    isFolder: true,
                })
            );
            const expected = getSettingsReadModelMock();
            const result = await settingsService.getSettings(UNIT_TEST_VARS.userId);

            expect(logger.warn).not.toHaveBeenCalled();
            expect(settingsRepository.getSettings).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });

        it('should successfully get settings without not required keys', async () => {
            const modelMock = getSettingsReadModelMock({ withRequiredOnly: true });

            delete modelMock.egMetadata.isNamespaceAdded;
            (settingsRepository.getSettings as jest.Mock).mockResolvedValue(modelMock);

            const expected = getSettingsReadModelMock({ withRequiredOnly: true });
            const result = await settingsService.getSettings(UNIT_TEST_VARS.userId);

            expect(logger.warn).not.toHaveBeenCalled();
            expect(settingsRepository.getSettings).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });

        it('should throw an error, because repository threw an error', async () => {
            const expected = new Error('No settings found');
            (settingsRepository.getSettings as jest.Mock).mockRejectedValue(expected);

            await expect(settingsService.getSettings(UNIT_TEST_VARS.userId)).rejects.toThrow(expected);
            expect(logger.warn).not.toHaveBeenCalled();
            expect(settingsRepository.getSettings).toHaveBeenCalledTimes(1);
        });

        it('should unset egUploadFolder settings field, because EgnyteClientService throws an error (when the chosen folder is already deleted)', async () => {
            (egnyteClientService.getFolderDetailsById as jest.Mock).mockRejectedValue(new Error('Folder deleted'));

            const expected = getSettingsReadModelMock({ withRequiredOnly: true });
            const result = await settingsService.getSettings(UNIT_TEST_VARS.userId);

            expect(result).toEqual(expected);
            expect(settingsRepository.deleteSettingsKey).toHaveBeenCalledTimes(1);
            expect(logger.warn).toHaveBeenCalled();
        });
    });

    describe('updateCommonSettings', () => {
        it('should successfully update settings with model containing all keys', async () => {
            const updateModel = getSettingsUpdateModelMock();
            await settingsService.updateCommonSettings(UNIT_TEST_VARS.userId, updateModel);

            expect(settingsRepository.updateSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId, updateModel);
        });

        it('should successfully update settings with model without any key', async () => {
            await settingsService.updateCommonSettings(UNIT_TEST_VARS.userId, {});

            expect(settingsRepository.updateSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId, {});
        });
    });

    describe('removeMetadataNamespace', () => {
        it('should successfully remove metadata', async () => {
            await settingsService.removeMetadataNamespace(UNIT_TEST_VARS.userId);
            const namespace = configService.get('egnyteMetadataNamespaceName');
            expect(settingsRepository.updateSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId, {
                egMetadata: { isSavingEnabled: false },
            });
            expect(egnyteClientService.removeMetadataNamespace).toHaveBeenCalledWith(namespace);
        });
    });

    describe('updateAdminSettings', () => {
        it.todo('Create updateAdminSettings method tests');
    });
});
