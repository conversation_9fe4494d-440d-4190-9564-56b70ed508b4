import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { URL } from 'url';

@Injectable()
export class AppRelative {
    private readonly httpRegex = /^http(s)?:\/\//;

    private domain: string;

    private basePath: string;

    private clientPath: string;

    constructor(private configService: ConfigService) {
        this.domain = configService.get('domain');
        this.basePath = configService.get('basePath');
        this.clientPath = configService.get('outlookClientPath');
    }

    fullyQualifiedByHostHeader(req: Request, relativeUrl = ''): string {
        if (!req?.headers?.host) {
            throw new Error('Host header missing');
        }

        const domain = this.getDomainWithHttps(req.headers.host);

        return this.getRelativeUrl(domain, `/outlook${relativeUrl}`);
    }

    fullyQualified(relativeUrl = ''): string {
        const domain = this.getDomainWithHttps(this.domain);

        return this.getRelativeUrl(domain, `/outlook${relativeUrl}`);
    }

    fullyQualifiedClient(relativeUrl = ''): string {
        const domain = this.getDomainWithHttps(this.domain);

        return this.getRelativeUrl(domain, `${this.clientPath}${relativeUrl}`);
    }

    domainRelative(relativeUrl: string): string {
        return `${this.basePath}${relativeUrl}`;
    }

    private getDomainWithHttps(domain: string): string {
        if (!this.httpRegex.test(domain)) {
            return `https://${domain}`;
        }

        return domain;
    }

    private getRelativeUrl(domain: string, relativeUrl: string) {
        const url = new URL(`${this.basePath}${relativeUrl}`, domain);

        return url.toString();
    }
}
