import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsNumber, IsString } from '@nestjs/class-validator';
import { Type } from 'class-transformer';

export class UploadAttachmentsMessageNestedDto {
    @ApiProperty()
    @IsString()
    public readonly msMessageId: string;

    @ApiProperty()
    @IsString()
    public readonly from: string;

    @ApiProperty()
    @IsString()
    public readonly to: string;

    @ApiProperty()
    @IsString()
    public readonly subject: string;

    @ApiProperty()
    @IsNumber()
    public readonly attachmentsNumber: number;

    @ApiProperty()
    @IsDate()
    @Type(() => Date)
    public readonly timestamp: Date;
}
