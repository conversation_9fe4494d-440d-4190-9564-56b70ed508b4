import { getSettingsReadModelMock } from 'ms-metaos-database/outlook';
import { IPublic } from '../../common/utils/public.interface';
import { SettingsService } from './settings.service';

export function getSettingsServiceMock(): IPublic<SettingsService> {
    return {
        createNamespace: jest.fn(),
        getSettings: jest.fn().mockResolvedValue(getSettingsReadModelMock()),
        updateCommonSettings: jest.fn().mockResolvedValue(getSettingsReadModelMock()),
        removeMetadataNamespace: jest.fn(),
    };
}
