import { MSGraph } from '@integrations/pint-ms';
import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import { Injectable, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PinoLogger } from 'nestjs-pino';
import { AuthRepository } from 'ms-metaos-database/outlook';
import { MsAuthService } from '../ms-auth/ms-auth.service';
import { CredentialsProvider } from '../credentials-provider/credentials-provider';

@Injectable({ scope: Scope.REQUEST })
export class MsGraphService {
    private readonly msGraphInstance: MSGraph;

    constructor(
        logger: PinoLogger,
        msAuthService: MsAuthService,
        config: ConfigService,
        authRepository: AuthRepository,
        private readonly credentialsProvider: CredentialsProvider
    ) {
        this.msGraphInstance = new MSGraph({
            getAccessTokenMethod: async () => {
                const credentials = await credentialsProvider.getCredentials();
                const tokenData: IGetTokenResponse = await msAuthService.getValidToken({
                    accessToken: credentials.microsoft.accessToken,
                    refreshToken: credentials.microsoft.refreshToken,
                    expireTime: credentials.microsoft.tokenExpiration,
                    scopes: config.get('msScopes'),
                    tenantId: credentials.microsoft.tenantId,
                    refreshThreshold: config.get('msAuthTokenRefreshThreshold'),
                    persistDataCallback: async (opts: IGetTokenResponse) => {
                        await authRepository.updateMsAuth(credentials.userId, {
                            refreshToken: opts.refreshToken,
                            tokenExpiration: opts.expireTime,
                            accessToken: opts.accessToken,
                        });
                    },
                });

                return tokenData.accessToken;
            },
            logger: {
                info: logger.info,
                error: logger.error,
                warn: logger.warn,
            },
            settings: {
                msGraphNextLinkLimit: 50,
                msGraphDisableCompression: false,
                msLogLevel: 'debug',
            },
        });
    }

    public async getAttachmentFile({
        messageId,
        attachmentId,
    }: {
        messageId: string;
        attachmentId: string;
    }): Promise<NodeJS.ReadableStream> {
        return this.msGraphInstance.mails.getAttachment(messageId, attachmentId);
    }

    public async getMessageFile(messageId: string): Promise<NodeJS.ReadableStream> {
        return this.msGraphInstance.mails.getMessage(messageId);
    }
}
