import { FilePickerService } from './file-picker.service';
import { IPublic } from '../../common/utils/public.interface';
import { getFilePickerBreadcrumbsReadModelMock } from '../models/file-picker-breadcrumbs/file-picker-breadcrumbs-read.mock';
import { getFilePickerItemReadModelMock } from '../models/file-picker-item/file-picker-item-read.mock';
import { getFilePickerPageReadModelMock } from '../models/file-picker-page/file-picker-page-read.mock';

export function getFilePickerServiceMock(): IPublic<FilePickerService> {
    return {
        getBookmarkedItems: jest.fn().mockResolvedValue(
            getFilePickerPageReadModelMock({
                itemsType: 'folders',
            })
        ),
        getDataByFolderId: jest.fn().mockResolvedValue(
            getFilePickerItemReadModelMock({
                isFolder: true,
                withChildren: true,
            })
        ),
        getDataByFolderPath: jest.fn().mockResolvedValue(
            getFilePickerItemReadModelMock({
                isFolder: true,
                withChildren: true,
            })
        ),
        searchForItems: jest
            .fn()
            .mockImplementation((query) =>
                query.searchType === 'FOLDER'
                    ? getFilePickerPageReadModelMock({ itemsType: 'folders' })
                    : getFilePickerPageReadModelMock()
            ),
        getRecentItems: jest.fn().mockResolvedValue(getFilePickerPageReadModelMock({ itemsType: 'files' })),
        getBreadcrumbsByFolderId: jest.fn().mockResolvedValue(getFilePickerBreadcrumbsReadModelMock()),
    };
}
