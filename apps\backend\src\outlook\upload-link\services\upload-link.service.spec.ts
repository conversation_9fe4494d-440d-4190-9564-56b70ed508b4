import { Test, TestingModule } from '@nestjs/testing';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import {
    getEgnyteClientServiceMock,
    testUploadLink,
} from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { UploadLinkService } from './upload-link.service';
import { SHARE_LINK_TYPE } from '../../share-link/enums/share-link-type.enum';

describe('UploadLinkService', () => {
    let testingModule: TestingModule;
    let uploadLinkService: UploadLinkService;

    beforeEach(async () => {
        jest.resetAllMocks();
        testingModule = await Test.createTestingModule({
            providers: [UploadLinkService, { provide: EgnyteClientService, useValue: getEgnyteClientServiceMock() }],
        }).compile();

        uploadLinkService = testingModule.get<UploadLinkService>(UploadLinkService);
    });

    describe('createUploadLink', () => {
        it('should create upload link', async () => {
            const result = await uploadLinkService.createUploadLink({
                item: {
                    path: '/Shared/Test-Folder',
                    folderPerRecipient: false,
                    id: 'someFolderId',
                    isFolder: false,
                },
                expiration: { isActive: false },
                shareType: SHARE_LINK_TYPE.ANYONE,
            });

            expect(result).toEqual(testUploadLink);
        });
    });
});
