import { EG_USER_TYPE } from '../../../common/consts/egnyte-user-type.const';
import { IEgnyteUserInfo } from '../../../common/interfaces/egnyte-provider.interface';
import { Mappers } from '../../../common/utils/mappers';
import { Helpers } from '../../../common/utils/helpers';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class AuthEGReadModel {
    @ValidateArgs()
    public static fromResponse(
        state: string,
        domain: string,
        // eslint-disable-next-line camelcase
        egAuthResult: { access_token: string },
        userInfo: IEgnyteUserInfo
    ): AuthEGReadModel {
        return {
            userId: state,
            domain: Helpers.sanitizeDomain(domain),
            accessToken: egAuthResult.access_token,
            id: userInfo.id,
            username: userInfo.username,
            userType: Mappers.mapRawToModelUserType(userInfo.user_type),
        };
    }

    public userId: string;

    public accessToken: string;

    public domain: string;

    public id: number;

    public username: string;

    public userType: EG_USER_TYPE;
}
