import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { UploadLinkItemNestedDto } from '../dtos/upload-link-item-nested.dto';

export class UploadLinkCreateItemModel {
    @ValidateArgs()
    public static fromDto(dto: UploadLinkItemNestedDto): UploadLinkCreateItemModel {
        return {
            id: dto.id,
            isFolder: dto.isFolder,
            folderPerRecipient: dto.folderPerRecipient || false,
            path: dto.path,
        };
    }

    public id: string;

    public isFolder: boolean;

    public folderPerRecipient: boolean;

    public path: string;
}
