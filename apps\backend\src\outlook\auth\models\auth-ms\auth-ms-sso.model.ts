import { AuthSetupReadModel } from 'ms-metaos-database/outlook';
import { DEFAULT_TENANT_ID } from '../../../common/consts/ms-auth.const';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class AuthMSSSOModel {
    @ValidateArgs()
    public static fromAuthSetupModel(data: AuthSetupReadModel): AuthMSSSOModel {
        const model = new AuthMSSSOModel();
        model.assertion = data.assertion;
        if (data.tenantId) {
            model.tenantId = data.tenantId;
        } else {
            model.tenantId = DEFAULT_TENANT_ID;
        }

        return {
            assertion: data.assertion,
            tenantId: data.tenantId ?? DEFAULT_TENANT_ID,
        };
    }

    public assertion: string;

    public tenantId?: string;
}
