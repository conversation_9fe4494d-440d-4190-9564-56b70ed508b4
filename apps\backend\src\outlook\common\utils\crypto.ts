import { createCipher<PERSON>, createDecipheriv, randomBytes } from 'crypto';

export class Crypto {
    private separator = ':';

    private ivLength = 16;

    private iv: Buffer;

    private key: Buffer;

    constructor(key: string, iv?: string) {
        this.key = Buffer.from(key, 'hex');

        if (iv) {
            this.iv = Buffer.from(iv, 'hex');
        }
    }

    encryptAes256(input: string): string {
        const iv = this.iv || this.randomIv();
        const cipher = createCipheriv('aes-256-ctr', this.key, iv);

        let crypted = cipher.update(input, 'utf8', 'base64');
        crypted += cipher.final('base64');

        return `${iv.toString('base64')}${this.separator}${crypted}`;
    }

    decryptAes256(input: string): string {
        const [ivBuffer, text] = input.split(this.separator);
        const iv = Buffer.from(ivBuffer, 'base64');
        const decipher = createDecipheriv('aes-256-ctr', this.key, iv);

        let decrypted = decipher.update(text, 'base64', 'utf8');
        decrypted += decipher.final('utf8');

        return decrypted;
    }

    private randomIv() {
        return randomBytes(this.ivLength);
    }
}
