import { IPublic } from './public.interface';
import { AppRelative } from './app-relative';

export function getAppRelativePartsMock(): Record<string, string> {
    return {
        domainMock: 'https://testDomain.com',
        basePathMock: 'testBasePath',
        clientPathMock: 'testClientPath',
    };
}

export function getAppRelativeMock(): IPublic<AppRelative> {
    const { domainMock, basePathMock, clientPathMock } = getAppRelativePartsMock();

    return {
        fullyQualifiedByHostHeader: jest
            .fn()
            .mockImplementation((path: string): string => `${domainMock}/${basePathMock}${path}`),
        fullyQualified: jest.fn().mockImplementation((path: string): string => `${domainMock}/${basePathMock}${path}`),
        fullyQualifiedClient: jest
            .fn()
            .mockImplementation((path: string): string => `${domainMock}/${basePathMock}/${clientPathMock}${path}`),
        domainRelative: jest.fn().mockImplementation((path: string): string => `${basePathMock}${path}`),
    };
}
