import { Injectable } from '@nestjs/common';
import * as Reporting from '@integrations/pint-reporting';
import { Request, Response, NextFunction } from 'express';
import utils from 'ms-metaos-utils';
import { ClsService } from 'nestjs-cls';
import { AuthReadModel, AdminAuthReadModel } from 'ms-metaos-database/teams';
import { MetricsMiddlewareFactory, ReportMetadata } from '../metrics.types';
import { IdentityReadModel } from '../../models/identity-read.model';

@Injectable()
export class ReportingService implements MetricsMiddlewareFactory {
    public static getEventFields(req: Request & { user: AuthReadModel }): ReportMetadata {
        return {
            userId: req?.user?.egUserId ?? 'n/a',
            domain: req?.user?.egDomain ?? 'n/a',
            username: req?.user?.egUsername ?? 'n/a',
            userAgent: `${req?.headers?.['user-agent'] ?? 'n/a'}`,
            ip: req?.headers?.['x-forwarded-for'],
            requestId: req?.headers?.['x-egnyte-request-id'] ?? 'n/a',
            tags: req?.headers['x-ms-context'] || null,
        };
    }

    constructor(private readonly clsService: ClsService) {}

    public getMiddleware(): (
        req: Request & {
            user: AuthReadModel;
        },
        res: Response,
        next: NextFunction
    ) => void {
        return Reporting.getMiddleware({
            includePath: true,
            excludePath: (req: Request & { user: AuthReadModel }) => this.excludePath(req),
            getPath: (
                req: Request & {
                    user: AuthReadModel;
                }
            ) => utils.sanitizer.normalizePath(req, { valueMasks: [/(\w|\d|-)=$/] }),
            getEventFields: (req: Request & { user: AuthReadModel }) => ReportingService.getEventFields(req),
        });
    }

    public processEvent(data: { action: string } & Record<string, unknown>): void {
        const defaults = this.getDefaultProcessEventFields();

        const mergedTags = data.tags
            ? [data.tags].concat(defaults.tags ?? []).toString() || 'n/a'
            : defaults.tags || 'n/a';

        Reporting.processEvent({ ...defaults, ...data, tags: mergedTags });
    }

    private excludePath(req: Request & { user: AuthReadModel }) {
        const pathsToExclude = ['/static', '/metrics', '/health', '/client'];

        return !!pathsToExclude.find((excludedRoute) => req.url.match(excludedRoute));
    }

    private getDefaultProcessEventFields(): ReportMetadata {
        const reporting =
            this.clsService.get<Pick<ReportMetadata, 'userAgent' | 'ip' | 'requestId' | 'tags'>>('reporting');
        const auth = this.clsService.get<AuthReadModel>('auth');
        const adminAuth = this.clsService.get<AdminAuthReadModel>('adminAuth');
        const identity = this.clsService.get<IdentityReadModel>('identity');

        return {
            userId: auth?.userId || adminAuth?.id || identity?.userId || 'n/a',
            domain: auth?.egDomain || adminAuth?.egDomain || 'n/a',
            username: auth?.egUsername || identity?.msEmail || 'n/a',
            userAgent: reporting.userAgent,
            ip: reporting.ip,
            requestId: reporting.requestId,
            tags: reporting.tags || null,
        };
    }
}
