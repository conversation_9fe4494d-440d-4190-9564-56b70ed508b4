import { HttpException, Injectable, InternalServerErrorException, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Egnyte from 'egnyte-js-sdk';
import * as pintlink from '@integrations/pint-link';
import { IncomingMessage } from 'http';
import { PinoLogger } from 'nestjs-pino';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { Helpers } from '../../utils/helpers';
import {
    IEgnyteAllowedLinkTypes,
    IEgnyteDefaultAttributes,
    IEgnyteInstance,
    IEgnyteListContents,
    IEgnytePerms,
    IEgnyteSdkItem,
    IEgnyteSdkMetadataNamespaceRead,
    IEgnyteSdkMetadataNamespaceWrite,
    IEgnyteSdkPromiseRequestBookmarks,
    IEgnyteSdkPromiseRequestRecentFiles,
    IEgnyteSdkResult,
    IEgnyteSearchResult,
    IEgnyteSearchUsersResult,
    IEgnyteSharingRestrictions,
    IEgnyteUploadLinkRequest,
    IEgnyteUploadLinkResponse,
} from '../../interfaces/egnyte-provider.interface';
import { CredentialsProvider } from '../credentials-provider/credentials-provider';
import { IPintLinkDomainSettings } from '../../interfaces/pint-link.interface';
import {
    IEgnyteMetadataNamespaceCreateModel,
    IEgnyteMetadataNamespaceUpdateModel,
} from '../../interfaces/egnyte-metadata-namespace-model.interface';
import { UserDetailsModel } from '../../models/user-details/user-details.model';
import { FilePickerPageReadModel } from '../../../file-picker/models/file-picker-page/file-picker-page-read.model';
import { FilePickerItemReadModel } from '../../../file-picker/models/file-picker-item/file-picker-item-read.model';
import { FilePickerInfoOptionsModel } from '../../../file-picker/models/file-picker-item/file-picker-info-options.model';
import { ShareLinkUsersReadModel } from '../../../share-link/models/share-link-recipients/share-link-users-read.model';
import { ShareLinkPermissionsReadModel } from '../../../share-link/models/share-link-permissions/share-link-permissions-read.model';
import { FilePickerSearchQueryDto } from '../../../file-picker/dtos/file-picker-search-query.dto';

@Injectable({ scope: Scope.REQUEST })
export class EgnyteClientService {
    private egnyteInstance: IEgnyteInstance;

    private entryPath: string;

    private credentials: AuthReadModel;

    constructor(
        private readonly egCredentialsProvider: CredentialsProvider,
        private readonly configService: ConfigService,
        private readonly logger: PinoLogger
    ) {}

    public async getFolderDetailsById(
        folderId: string,
        options: FilePickerInfoOptionsModel
    ): Promise<FilePickerItemReadModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const params: Record<string, string> = {
            list_content: String(options.withChildren),
        };

        const url = `https://${
            credentials.egnyte.domain
        }/pubapi/v1/fs/ids/folder/${folderId}?${Helpers.buildQueryString(params)}`;

        try {
            const result = await egnyteInstance.API.manual.promiseRequest<
                IEgnyteSdkResult<IEgnyteListContents<Record<string, unknown>>>
            >({
                url,
            });

            return FilePickerItemReadModel.fromSDKResult(result.body);
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting folder details by id. Folder id: ${folderId}. Error: ${
                    e.message ?? 'Unknown'
                }`
            );
            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getFolderDetailsByPath(
        folderPath: string,
        options: FilePickerInfoOptionsModel
    ): Promise<FilePickerItemReadModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const params: Record<string, string> = {
            list_content: String(options.withChildren),
        };

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/fs/${Helpers.sanitizePathUrl(
            folderPath
        )}?${Helpers.buildQueryString(params)}`;

        try {
            const result = await egnyteInstance.API.manual.promiseRequest<
                IEgnyteSdkResult<IEgnyteListContents<Record<string, unknown>>>
            >({
                url,
            });

            return FilePickerItemReadModel.fromSDKResult(result.body);
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting folder details by path. Folder path: ${folderPath}. Error: ${
                    e.message ?? 'Unknown'
                }`
            );
            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getBookmarkedItems(): Promise<FilePickerPageReadModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/bookmarks`;

        try {
            const result = await egnyteInstance.API.manual.promiseRequest<IEgnyteSdkPromiseRequestBookmarks>({
                url,
            });

            return FilePickerPageReadModel.fromBookmarksResponse(result.body);
        } catch (e) {
            this.logger.error(`EgnyteAPI error: getting bookmarks. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async searchForItems(searchQuery: FilePickerSearchQueryDto): Promise<FilePickerPageReadModel> {
        const egnyteInstance = await this.getInstance();

        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v2/search`;

        try {
            const result = await egnyteInstance.API.manual.promiseRequest<IEgnyteSearchResult>({
                url,
                method: 'POST',
                json: {
                    query: searchQuery.q,
                    offset: 0,
                    count: 20,
                    type: searchQuery.searchType,
                },
            });

            return FilePickerPageReadModel.fromSearchResultsResponse(result.body);
        } catch (e) {
            this.logger.error(`EgnyteAPI error: search. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async searchForUsers(queryString: string): Promise<ShareLinkUsersReadModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();
        const url = `https://${credentials.egnyte.domain}/pubapi/v2/users`;

        let response: { response: IncomingMessage; body: IEgnyteSearchUsersResult };

        try {
            response = await egnyteInstance.API.manual.promiseRequest<IEgnyteSearchUsersResult>({
                url,
                params: {
                    filter: Helpers.isEmail(queryString) ? `email eq "${queryString}"` : `userName sw "${queryString}"`,
                },
            });
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: search for users. Query string: ${queryString}. Error: ${e.message ?? 'Unknown'}`
            );

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }

        return ShareLinkUsersReadModel.fromResponse(response.body, credentials);
    }

    public async getRecentItems(): Promise<FilePickerPageReadModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/insights/files`;

        try {
            const result = await egnyteInstance.API.manual.promiseRequest<IEgnyteSdkPromiseRequestRecentFiles>({
                url,
            });

            return FilePickerPageReadModel.fromRecentsResponse(result.body);
        } catch (e) {
            this.logger.error(`EgnyteAPI error: getting recent files. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getUserInfo(): Promise<UserDetailsModel> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();
        try {
            const result = await egnyteInstance.API.auth.getUserInfo();

            return UserDetailsModel.fromAuthResponse(result, credentials);
        } catch (e) {
            this.logger.error(`EgnyteAPI error: getting user info. Error: ${e.message ?? 'Unknown'}`);
            if (e.statusCode === 0) {
                this.logger.error(`EgnyteAPI error. domain doesn't exist in Egnyte`);
                throw new HttpException(`User domain doesn't exist`, 410);
            }
            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async uploadFile(path: string, fileData: NodeJS.ReadableStream): Promise<FilePickerItemReadModel> {
        const egnyteInstance = await this.getInstance();

        try {
            const result = await egnyteInstance.API.storage.requestId(this.getTraceId()).path(path).storeFile(fileData);

            return FilePickerItemReadModel.fromItemWithPath(result);
        } catch (e) {
            this.logger.error(`EgnyteAPI error: upload file. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async deleteFile(filePath: string): Promise<void> {
        const egnyteInstance = await this.getInstance();

        try {
            await egnyteInstance.API.storage.requestId(this.getTraceId()).path(filePath).remove();
        } catch (e) {
            this.logger.error(`EgnyteAPI error: remove file. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getFileDefaultAttributes(entryId: string) {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const defaultAttributesUrl = `https://${credentials.egnyte.domain}/pubapi/v2/links/default_attributes/files`;

        let defaultAttributes: {
            response: IncomingMessage;
            body: IEgnyteDefaultAttributes;
        };

        try {
            defaultAttributes = await egnyteInstance.API.manual.promiseRequest<IEgnyteDefaultAttributes>({
                url: defaultAttributesUrl,
                method: 'POST',
                json: [entryId],
            });

            this.logger.info(`EgnyteAPI file default attributes: ${JSON.stringify(defaultAttributes.body)}`);

            return defaultAttributes.body;
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting file/folder default attributes. File id: ${entryId}. Error: ${
                    e.message ?? 'Unknown'
                }`
            );

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getFileSharingRestrictions(entryId: string) {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const sharingRestrictionsUrl = `https://${credentials.egnyte.domain}/pubapi/v2/links/sharing_restrictions/files`;
        let sharingRestrictions: {
            response: IncomingMessage;
            body: IEgnyteSharingRestrictions;
        };

        try {
            sharingRestrictions = await egnyteInstance.API.manual.promiseRequest<IEgnyteSharingRestrictions>({
                url: sharingRestrictionsUrl,
                method: 'POST',
                json: [entryId],
            });

            this.logger.info(`EgnyteAPI file sharing restrictions: ${JSON.stringify(sharingRestrictions.body)}`);

            return sharingRestrictions.body;
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting file sharing restrictions. File id: ${entryId}. Error: ${
                    e.message ?? 'Unknown'
                }`
            );

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getPermissionsForFile(fileId: string) {
        const credentials = await this.getUserCredentials();
        const fileData = await this.getFileDetailsById(fileId);
        const entryId = fileData.entry_id;

        const supportedShareLinks = this.configService.get('supportedShareLinks');

        if (credentials.egnyte.userType === 'standard') {
            this.logger.info(
                `Getting domain settings for standard user. Domain: ${credentials.egnyte.domain}. File id (groupId): ${fileId}.`
            );

            return ShareLinkPermissionsReadModel.fromResponse(supportedShareLinks, {});
        }

        const defaultAttributes = await this.getFileDefaultAttributes(entryId);
        const sharingRestrictions = await this.getFileSharingRestrictions(entryId);

        return ShareLinkPermissionsReadModel.fromResponse(supportedShareLinks, {
            defaultAttributes,
            sharingRestrictions,
        });
    }

    public async getPermissionsForFolder(folderId: string) {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const permissionsParams: Record<string, string> = {
            list_content: String(false),
            allowed_link_types: String(true),
            include_perm: String(true),
        };
        const permissionsUrl = `https://${
            credentials.egnyte.domain
        }/pubapi/v1/fs/ids/folder/${folderId}?${Helpers.buildQueryString(permissionsParams)}`;

        let folderData: IEgnyteSdkResult<IEgnyteAllowedLinkTypes & IEgnytePerms>;
        let result: { response: IncomingMessage; body: typeof folderData };

        try {
            result = await egnyteInstance.API.manual.promiseRequest<
                IEgnyteSdkResult<IEgnyteAllowedLinkTypes & IEgnytePerms>
            >({ url: permissionsUrl });
            folderData = result.body;

            this.logger.info(`EgnyteAPI parent folder permissions: ${JSON.stringify(result.body)}`);
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting folder permissions. Folder id: ${folderId}. Error: ${e.message ?? 'Unknown'}`
            );

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }

        const supportedShareLinks = this.configService.get('supportedShareLinks');

        if (credentials.egnyte.userType === 'standard') {
            this.logger.info(
                `Getting domain settings for standard user. Domain: ${credentials.egnyte.domain}. Folder id: ${folderId}.`
            );

            return ShareLinkPermissionsReadModel.fromResponse(supportedShareLinks, { folderData });
        }

        // Both default_attributes and sharing_restrictions endpoints doesn't provide accessibility information for folders.
        // What they do provide, is share links default values and restrictions, also in terms of expiration, for any entry_id in valid UUID format,
        // regardless if the file with provided entry_id exists or not. That is also a missing part in domain settings and folder data endpoints -
        // - that is why we use fake entry_id to fetch those data for folders.
        const FAKE_ENTRY_ID = '00000000-0000-0000-0000-000000000000'; // keep this in UUID format
        const defaultAttributes = await this.getFileDefaultAttributes(FAKE_ENTRY_ID);
        const sharingRestrictions = await this.getFileSharingRestrictions(FAKE_ENTRY_ID);

        let domainSettings: IPintLinkDomainSettings | undefined;
        try {
            domainSettings = await pintlink.getSettings(egnyteInstance, supportedShareLinks);

            this.logger.info(`EgnyteAPI domain permissions: ${JSON.stringify(domainSettings)}`);
        } catch (err) {
            if (err.harmless) {
                this.logger.warn(
                    `EgnyteAPI error (harmless): getting domain settings for creating link. Domain: ${
                        credentials.egnyte.domain
                    }. Folder id: ${folderId}. Error: ${err.message ?? 'Unknown'}`
                );
            } else {
                this.logger.error(
                    `EgnyteAPI error: getting domain settings for creating link. Domain: ${
                        credentials.egnyte.domain
                    }. Folder id: ${folderId}. Error: ${err.message ?? 'Unknown'}`
                );
                throw new InternalServerErrorException('Getting domain settings error');
            }
        }

        return ShareLinkPermissionsReadModel.fromResponse(supportedShareLinks, {
            defaultAttributes,
            sharingRestrictions,
            folderData: result.body,
            domainSettings,
        });
    }

    public async getPermissionsForItemId({
        folderId,
        fileId,
    }: {
        folderId?: string;
        fileId?: string;
    }): Promise<ShareLinkPermissionsReadModel> {
        if (fileId) {
            return this.getPermissionsForFile(fileId);
        }

        return this.getPermissionsForFolder(folderId);
    }

    public async getMetadataNamespaces(): Promise<IEgnyteSdkMetadataNamespaceRead[]> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/properties/namespace`;

        let result: IEgnyteSdkMetadataNamespaceRead[];
        try {
            const response = await egnyteInstance.API.manual.promiseRequest<IEgnyteSdkMetadataNamespaceRead[]>({
                url,
            });
            result = response.body;
        } catch (e) {
            this.logger.error(`EgnyteAPI error: getting namespaces. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }

        return result;
    }

    public async addMetadataNamespace(namespaceData: IEgnyteMetadataNamespaceCreateModel): Promise<void> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/properties/namespace`;

        const mappedData: IEgnyteSdkMetadataNamespaceWrite = {
            name: namespaceData.name,
            scope: namespaceData.scope,
            keys: Object.keys(namespaceData.keys).reduce((accumulator, key) => {
                accumulator[key] = {
                    type: namespaceData.keys[key].type,
                    ...('data' in namespaceData.keys[key] && {
                        data: namespaceData.keys[key].data,
                    }),
                    ...('displayName' in namespaceData.keys[key] && {
                        displayName: namespaceData.keys[key].displayName,
                    }),
                    ...('priority' in namespaceData.keys[key] && {
                        priority: namespaceData.keys[key].priority,
                    }),
                    ...('helpText' in namespaceData.keys[key] && {
                        helpText: namespaceData.keys[key].helpText,
                    }),
                };

                return accumulator;
            }, {}),
            ...('displayName' in namespaceData && { displayName: namespaceData.displayName }),
            ...('priority' in namespaceData && { priority: namespaceData.priority }),
        };
        try {
            await egnyteInstance.API.manual.promiseRequest<IEgnyteSdkMetadataNamespaceRead>({
                url,
                method: 'POST',
                json: mappedData,
            });
        } catch (e) {
            this.logger.error(`EgnyteAPI error: getting namespaces. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async removeMetadataNamespace(namespaceName: string): Promise<void> {
        try {
            const egnyteInstance = await this.getInstance();
            const credentials = await this.getUserCredentials();

            const url = `https://${credentials.egnyte.domain}/pubapi/v1/properties/namespace/${namespaceName}`;

            await egnyteInstance.API.manual.promiseRequest({
                url,
                method: 'DELETE',
                headers: {
                    'X-Egnyte-Force-Delete': 'Yes',
                },
            });
        } catch (e) {
            this.logger.error(`EgnyteAPI error: remove namespaces. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async addMetadataKeysValues(
        fileId: string,
        namespaceData: IEgnyteMetadataNamespaceUpdateModel
    ): Promise<void> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const url = `https://${credentials.egnyte.domain}/pubapi/v1/fs/ids/file/${fileId}/properties/${namespaceData.name}`;

        try {
            const keysValuesData = Object.keys(namespaceData.keys).reduce((accumulator, key) => {
                accumulator[key] = namespaceData.keys[key];

                return accumulator;
            }, {});

            await egnyteInstance.API.manual.promiseRequest<void>({
                url,
                method: 'PUT',
                json: keysValuesData,
            });
        } catch (e) {
            if (e.statusCode === 404 && e.message === 'There is no custom metadata associated with this namespace') {
                this.logger.warn(`Outlook metadata namespace not found on ${credentials.egnyte.domain}`);

                return;
            }
            this.logger.error(`EgnyteAPI error: saving namespace keys values. Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async createUploadLink(data: IEgnyteUploadLinkRequest): Promise<IEgnyteUploadLinkResponse> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();
        const url = `https://${credentials.egnyte.domain}/pubapi/v1/links`;

        try {
            const { path, expiryDate, folderPerRecipient } = data;
            const result = await egnyteInstance.API.manual.promiseRequest<IEgnyteUploadLinkResponse>({
                url,
                method: 'POST',
                json: {
                    path,
                    type: 'upload',
                    ...(expiryDate && { expiry_date: expiryDate }),
                    ...(folderPerRecipient !== undefined && { folder_per_recipient: folderPerRecipient }),
                    notify: true,
                },
            });

            this.logger.info(`EgnyteAPI: Upload link created for path: ${path}`);

            return result.body;
        } catch (e) {
            this.logger.error(`EgnyteAPI error (Upload link). Error: ${e.message ?? 'Unknown'}`);

            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getInstance(): Promise<IEgnyteInstance> {
        if (!this.egnyteInstance) {
            await this.initInstance();
        }

        return this.egnyteInstance;
    }

    private async getFileDetailsById(fileId: string): Promise<IEgnyteSdkItem<void>> {
        const egnyteInstance = await this.getInstance();
        const credentials = await this.getUserCredentials();

        const getParentParams: Record<string, string> = {
            list_content: String(false),
        };

        const getParentUrl = `https://${
            credentials.egnyte.domain
        }/pubapi/v1/fs/ids/file/${fileId}?${Helpers.buildQueryString(getParentParams)}`;

        try {
            const parentResult = await egnyteInstance.API.manual.promiseRequest<IEgnyteSdkItem<void>>({
                url: getParentUrl,
            });

            return parentResult.body;
        } catch (e) {
            this.logger.error(
                `EgnyteAPI error: getting file details. File id: ${fileId}. Error: ${e.message ?? 'Unknown'}`
            );
            throw new HttpException(`EgnyteAPI error. Error: ${e.message ?? 'Unknown'}`, e.statusCode ?? 500);
        }
    }

    public async getUserCredentials(): Promise<AuthReadModel> {
        if (!this.credentials) {
            this.credentials = await this.egCredentialsProvider.getCredentials();
        }

        return this.credentials;
    }

    private async initInstance(): Promise<void> {
        const fullCredentials = await this.getUserCredentials();
        const { accessToken, domain } = fullCredentials.egnyte;
        const QPS = this.configService.get('qps') || 5;
        const handleQuota = this.configService.get('handleQuota') || true;
        const egnyteInstance = Egnyte.init(Helpers.normalizeDomain(domain), {
            token: accessToken,
            QPS,
            handleQuota,
        });
        this.entryPath = '/';
        this.egnyteInstance = egnyteInstance;
    }

    private getTraceId(): string {
        const bindings: { traceId?: string; [key: string]: string | number } = this.logger.logger.bindings();

        return bindings.traceId ?? 'n/a';
    }
}
