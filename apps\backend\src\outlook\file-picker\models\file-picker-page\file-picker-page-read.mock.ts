import { FilePickerPageReadModel } from './file-picker-page-read.model';
import { getFilePickerItemReadModelMock } from '../file-picker-item/file-picker-item-read.mock';
import { FilePickerItemReadModel } from '../file-picker-item/file-picker-item-read.model';

function getFilePickerPageReadModelMockItems(type: 'mixed' | 'files' | 'folders' = 'mixed'): FilePickerItemReadModel[] {
    switch (type) {
        case 'files':
            return [
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName1',
                    fileId: 'testPageFileId1',
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName2',
                    fileId: 'testPageFileId2',
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName3',
                    fileId: 'testPageFileId3',
                }),
            ];
        case 'folders':
            return [
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName1',
                    folderId: 'testPageFolderId1',
                    isFolder: true,
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName2',
                    folderId: 'testPageFolderId2',
                    isFolder: true,
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName3',
                    folderId: 'testPageFolderId3',
                    isFolder: true,
                }),
            ];
        default:
            return [
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName1',
                    folderId: 'testPageFolderId1',
                    isFolder: true,
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName2',
                    fileId: 'testPageFileId2',
                }),
                getFilePickerItemReadModelMock({
                    name: 'testPageItemName3',
                    fileId: 'testPageFileId3',
                }),
            ];
    }
}

export function getFilePickerPageReadModelMock({
    itemsType = 'mixed',
}: { itemsType?: 'mixed' | 'files' | 'folders' } = {}): FilePickerPageReadModel {
    const items = getFilePickerPageReadModelMockItems(itemsType);

    return {
        offset: 0,
        total: items.length,
        count: items.length,
        items,
    };
}
