/* eslint-disable max-classes-per-file */
import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { SHARE_LINK_TYPE } from '../enums/share-link-type.enum';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import {
    ShareLinkPermissionsReadExpirationDefaultModel,
    ShareLinkPermissionsReadExpirationMaxAllowedModel,
    ShareLinkPermissionsReadModel,
} from '../models/share-link-permissions/share-link-permissions-read.model';

class ShareLinkPermissionsExpirationMaxAllowedNestedDto {
    @ValidateArgs()
    public static fromResponse(
        data: ShareLinkPermissionsReadExpirationMaxAllowedModel
    ): ShareLinkPermissionsExpirationMaxAllowedNestedDto {
        return {
            expiryTimeValue: data.expiryTimeValue,
            expiryTimeUnit: data.expiryTimeUnit,
            expiryClicks: data.expiryClicks,
        };
    }

    @ApiProperty()
    public expiryTimeValue: number;

    @ApiProperty({ type: String, enum: ['days', 'week', 'months'] })
    public expiryTimeUnit: string;

    @ApiProperty()
    public expiryClicks: number;
}

class ShareLinkPermissionsExpirationDefaultNestedDto {
    @ValidateArgs()
    public static fromResponse(
        data: ShareLinkPermissionsReadExpirationDefaultModel
    ): ShareLinkPermissionsExpirationDefaultNestedDto {
        return {
            value: data.value,
            unit: data.unit,
        };
    }

    @ApiProperty()
    public value: number;

    @ApiProperty({ type: String, enum: ['days', 'week', 'months', 'click'] })
    public unit: string;
}

export class ShareLinkPermissionsExpirationNestedDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkPermissionsReadModel): ShareLinkPermissionsExpirationNestedDto {
        return {
            maxAllowed: Object.fromEntries(
                Object.entries(data.expiration.maxAllowed).map(([key, value]) => [
                    key,
                    value ? ShareLinkPermissionsExpirationMaxAllowedNestedDto.fromResponse(value) : null,
                ])
            ) as Record<SHARE_LINK_TYPE, ShareLinkPermissionsExpirationMaxAllowedNestedDto>,
            default: Object.fromEntries(
                Object.entries(data.expiration.default).map(([key, value]) => [
                    key,
                    value ? ShareLinkPermissionsExpirationDefaultNestedDto.fromResponse(value) : null,
                ])
            ) as Record<SHARE_LINK_TYPE, ShareLinkPermissionsExpirationDefaultNestedDto>,
        };
    }

    @ApiProperty({
        type: 'object',
        additionalProperties: {
            $ref: getSchemaPath(ShareLinkPermissionsExpirationMaxAllowedNestedDto),
            nullable: true,
        },
    })
    public maxAllowed: Record<SHARE_LINK_TYPE, ShareLinkPermissionsExpirationMaxAllowedNestedDto | null>;

    @ApiProperty({
        type: 'object',
        additionalProperties: { $ref: getSchemaPath(ShareLinkPermissionsExpirationDefaultNestedDto), nullable: true },
    })
    public default: Record<SHARE_LINK_TYPE, ShareLinkPermissionsExpirationDefaultNestedDto | null>;
}
