import { ApiProperty } from '@nestjs/swagger';
import { FilePickerFolderBreadcrumbNestedDto } from './file-picker-folder-breadcrumb-nested.dto';
import { FilePickerBreadcrumbsReadModel } from '../models/file-picker-breadcrumbs/file-picker-breadcrumbs-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class FilePickerFolderBreadcrumbsResponseDto {
    @ValidateArgs()
    public static fromResponse(data: FilePickerBreadcrumbsReadModel): FilePickerFolderBreadcrumbsResponseDto {
        return {
            folderId: data.folderId,
            path: data.path,
            breadcrumbs: data.items.map((item) => FilePickerFolderBreadcrumbNestedDto.fromResponse(item)),
        };
    }

    @ApiProperty()
    public folderId: string;

    @ApiProperty()
    public path: string;

    @ApiProperty({ type: FilePickerFolderBreadcrumbNestedDto, isArray: true })
    public breadcrumbs: FilePickerFolderBreadcrumbNestedDto[];
}
