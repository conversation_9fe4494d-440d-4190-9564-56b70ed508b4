import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseAuthModule } from 'ms-metaos-database/outlook';
import { EgnyteClientService } from './egnyte-client.service';
import { CredentialsProvider } from '../credentials-provider/credentials-provider';

@Module({
    imports: [DatabaseAuthModule],
    providers: [CredentialsProvider, EgnyteClientService],
    exports: [EgnyteClientService],
})
export class EgnyteClientServiceModule {}
