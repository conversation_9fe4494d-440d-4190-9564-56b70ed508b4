import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SettingsReadModel } from 'ms-metaos-database/outlook';
import { SettingsGetEgUploadFolderResponseNestedDto } from './settings-get-eg-upload-folder-response-nested.dto';
import { SettingsGetEgMetadataResponseNestedDto } from './settings-get-eg-metadata-response-nested.dto';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class SettingsGetResponseDto {
    @ValidateArgs()
    public static fromResponse(data: SettingsReadModel): SettingsGetResponseDto {
        return {
            ...(data.egUploadFolder && {
                egUploadFolder: SettingsGetEgUploadFolderResponseNestedDto.fromResponse(data.egUploadFolder),
            }),
            egMetadata: SettingsGetEgMetadataResponseNestedDto.fromResponse(data.egMetadata),
        };
    }

    @ApiPropertyOptional({ type: SettingsGetEgUploadFolderResponseNestedDto })
    public egUploadFolder?: SettingsGetEgUploadFolderResponseNestedDto;

    @ApiProperty({ type: SettingsGetEgMetadataResponseNestedDto })
    public egMetadata: SettingsGetEgMetadataResponseNestedDto;
}
