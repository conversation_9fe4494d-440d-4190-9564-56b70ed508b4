import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean } from '@nestjs/class-validator';
import { TransformBooleanStringToBoolean } from 'ms-metaos-decorators';

export class EgAuthFinishQueryDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public state?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    @TransformBooleanStringToBoolean()
    public updateAdminToken?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public error?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public code?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public domain?: string;
}
