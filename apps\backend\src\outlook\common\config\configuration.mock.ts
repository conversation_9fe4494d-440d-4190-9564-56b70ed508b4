function getConfigurationMock(): Record<string, unknown> {
    return new Proxy(
        {},
        {
            // eslint-disable-next-line no-empty-pattern
            get: ({}, prop: string | symbol) =>
                `test${prop.toString().charAt(0).toUpperCase()}${prop.toString().slice(1)}`,
        }
    );
}

export function getConfigServiceMock(): { get: jest.Mock<any, any, any>; getOrThrow: jest.Mock<any, any, any> } {
    const proxy = getConfigurationMock();

    return {
        get: jest.fn().mockImplementation((prop) => proxy[prop]),
        getOrThrow: jest.fn().mockImplementation((prop) => prop),
    };
}
