import {
    Body,
    Controller,
    ForbiddenException,
    Get,
    GoneException,
    HttpException,
    Post,
    Query,
    Redirect,
    UseGuards,
} from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ApiForbiddenResponse, ApiHeader, ApiResponse, ApiTags } from '@nestjs/swagger';
import { inspect } from 'util';
import {
    AuthReadModel,
    AuthSetupCreateModel,
    AuthSetupReadModel,
    AuthSetupUpdateModel,
} from 'ms-metaos-database/outlook';
import { AuthService } from '../services/auth.service';
import { AppRelative } from '../../common/utils/app-relative';
import { AuthCodeQueryDto } from '../dtos/auth-code-query.dto';
import { EgnyteAuthService } from '../services/egnyte-auth.service';
import { MsAuthService } from '../../common/services/ms-auth/ms-auth.service';
import { Identity } from '../decorators/identity.decorator';
import { AuthSetupRequestDto } from '../dtos/auth-setup-request.dto';
import { IdentityGuard } from '../guards/identity.guard';
import { AuthLoginResponseDto } from '../dtos/auth-login-response.dto';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { Auth } from '../../common/decorators/auth.decorator';
import { AuthUserDetailsResponseDto } from '../dtos/auth-user-details-response.dto';
import { AuthInitResponseDto } from '../dtos/auth-init-response.dto';
import { AuthRedirectResponseDto } from '../dtos/auth-redirect-response.dto';
import { IgnoreLogging, IgnoreLoggingOptions } from '../../common/decorators/ignore-logging.decorator';
import { IdentityModel } from '../../common/models/identity/identity.model';
import { AuthMSSSOModel } from '../models/auth-ms/auth-ms-sso.model';
import { AuthMSCodeModel } from '../models/auth-ms/auth-ms-code.model';
import { AuthEGCreateModel } from '../models/auth-eg/auth-eg-create.model';

@Controller('outlook/auth')
@ApiTags('AuthController')
export class AuthController {
    constructor(
        private readonly egnyteAuthService: EgnyteAuthService,
        private readonly authService: AuthService,
        private readonly msAuthService: MsAuthService,
        private readonly appRelative: AppRelative,
        private readonly logger: PinoLogger
    ) {
        logger.setContext(AuthController.name);
    }

    @Get('init')
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    async init() {
        return this.msAuthService.getInitialAuthData();
    }

    @ApiHeader({
        name: 'Authorization',
        description: 'Contains MS identity token',
    })
    @Post('setup')
    @UseGuards(IdentityGuard)
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({ status: 201, type: AuthInitResponseDto })
    async setup(@Identity() identity: IdentityModel, @Body() body: AuthSetupRequestDto): Promise<AuthInitResponseDto> {
        this.logger.assign({ userId: identity.userId, msTenantId: identity.tenantId });

        const data = AuthSetupCreateModel.fromDto(body, identity);

        const response = await this.authService.setupAuthData(data);

        return AuthInitResponseDto.fromResponse(response);
    }

    @Get('start')
    @Redirect()
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 302,
        description: 'Performs MS authorization and then redirects back to API endpoint',
    })
    async startAuth(@Query('userId') userId?: string): Promise<AuthRedirectResponseDto> {
        this.logger.assign({ userId });

        let authSetupRead: AuthSetupReadModel;

        try {
            authSetupRead = await this.authService.getAuthSetup(userId);
        } catch (error) {
            const params = new URLSearchParams({
                error: error instanceof GoneException ? 'Authorization process expired' : error.message,
            });

            const errorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

            return AuthRedirectResponseDto.fromData(302, errorUrl);
        }

        this.logger.assign({ msTenantId: authSetupRead.tenantId });

        if (authSetupRead.assertion) {
            this.logger.info(`Auth - found assertion. Redirect to Egnyte authorization. UserId: ${userId}`);

            try {
                const authMSSSO = AuthMSSSOModel.fromAuthSetupModel(authSetupRead);
                const authResult = await this.msAuthService.handleOnBehalfOf(authMSSSO);

                const authSetupUpdate = AuthSetupUpdateModel.fromAuthMSModel(authResult, userId);
                await this.authService.addMsAuthSetup(authSetupUpdate);

                this.logger.info(`Auth - MS on behalf login success. UserId: ${userId}`);

                const redirectUrl = this.appRelative.fullyQualified('/auth/eg-auth-finish');
                const url = this.egnyteAuthService.getAuthUrl(userId, redirectUrl);

                return AuthRedirectResponseDto.fromData(302, url);
            } catch (err) {
                this.logger.error(`Auth - MS on behalf login error: ${inspect(err)}`);

                if (err instanceof GoneException) {
                    const params = new URLSearchParams({
                        error: 'Authorization process expired',
                    });

                    const errorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

                    return AuthRedirectResponseDto.fromData(302, errorUrl);
                }
            }
        }

        this.logger.info(`Auth - not found assertion. Redirect to MS authorization. UserId: ${userId}`);

        const redirectUri = this.appRelative.fullyQualified('/auth/ms-auth-finish');

        const url = this.msAuthService.getRedirectToMsAuthorize({
            state: userId,
            redirectUri,
            loginHint: authSetupRead.msEmail,
        });

        return AuthRedirectResponseDto.fromData(302, url);
    }

    @Get('ms-auth-finish')
    @Redirect()
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 302,
        description:
            'Redirects back to MS Outlook Addin Frontend on error or saves MS auth data on success and then redirects to /auth/eg-auth-finish',
    })
    async getMsCode(@Query() query: AuthCodeQueryDto): Promise<AuthRedirectResponseDto> {
        if (query.error) {
            return this.handleGettingMsCodeError(query);
        }

        const userId = query.state;

        this.logger.assign({ userId });
        this.logger.info(`MS auth finish success. UserId: ${userId}`);

        const redirectUrl = this.appRelative.fullyQualified('/auth/eg-auth-finish');
        const egAuthUrl = this.egnyteAuthService.getAuthUrl(userId, redirectUrl);

        let authSetupRead: AuthSetupReadModel;

        try {
            authSetupRead = await this.authService.getAuthSetup(userId);
        } catch (error) {
            const params = new URLSearchParams({
                error: error instanceof GoneException ? 'Authorization process expired' : error.message,
            });

            const errorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

            return AuthRedirectResponseDto.fromData(302, errorUrl);
        }

        this.logger.assign({ msTenantId: authSetupRead.tenantId });

        const msUrl = this.appRelative.fullyQualified('/auth/ms-auth-finish');

        const authMSCode = AuthMSCodeModel.fromData({
            code: query.code,
            redirectUri: msUrl,
            tenantId: authSetupRead.tenantId,
        });
        const authResult = await this.msAuthService.handleAuthCode(authMSCode);

        this.logger.info(`MS handle AuthCode finish success`);

        try {
            await this.msAuthService.isUserEmailValid(authResult.accessToken, authSetupRead.msEmail, userId);

            const authSetupUpdate = AuthSetupUpdateModel.fromAuthMSModel(authResult, userId);
            await this.authService.addMsAuthSetup(authSetupUpdate);
        } catch (e) {
            const params = new URLSearchParams({
                error: e.message,
            });

            const errorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

            return AuthRedirectResponseDto.fromData(302, errorUrl);
        }

        return AuthRedirectResponseDto.fromData(302, egAuthUrl);
    }

    @Get('eg-auth-finish')
    @Redirect()
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 302,
        description: 'Performs EG authorization, saves data and then redirects back to MS Outlook Addin frontend',
    })
    async getCode(@Query() query: AuthCodeQueryDto): Promise<AuthRedirectResponseDto> {
        const redirectUrl = this.appRelative.fullyQualified('/auth/eg-auth-finish');

        let url = this.appRelative.fullyQualifiedClient('/loginConfirmation');

        try {
            const userId = decodeURIComponent(query.state);

            this.logger.assign({ userId });

            const authEGCreate = AuthEGCreateModel.fromDto(query, redirectUrl);
            const authResult = await this.egnyteAuthService.handleAuthCode(authEGCreate);

            const authSetupUpdate = AuthSetupUpdateModel.fromAuthEGModel(authResult);
            await this.authService.addEgAuthSetup(authSetupUpdate);

            await this.authService.finalizeAuth(userId);
        } catch (err) {
            this.logger.error(`Failed to authorize to Egnyte. Error: ${inspect(err)}`);

            let errorMessage: string;

            if (err instanceof GoneException) {
                errorMessage = 'Authorization process expired';
            } else if (err instanceof HttpException) {
                errorMessage = err.message;
            } else {
                errorMessage = 'Failed to authorize to Egnyte';
            }

            const params = new URLSearchParams({
                error: errorMessage,
            });

            url = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);
        }

        return AuthRedirectResponseDto.fromData(302, url);
    }

    @Get('ms-consent-start')
    @Redirect()
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 302,
        description: 'Starts series of redirects to perform admin consents for MSGraphAPI',
    })
    async getMsConsent(): Promise<AuthRedirectResponseDto> {
        const redirectUri = this.appRelative.fullyQualifiedClient('/loginConfirmation');
        const url = this.msAuthService.getRedirectToConsent({ redirectUri });

        this.logger.info(`Prepared redirect URI for MS conset`);

        return AuthRedirectResponseDto.fromData(302, url);
    }

    @ApiHeader({
        name: 'Authorization',
        description: 'Contains MS identity token',
    })
    @Post('login')
    @UseGuards(IdentityGuard)
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 200,
        type: AuthLoginResponseDto,
    })
    async login(@Identity() identity: IdentityModel): Promise<AuthLoginResponseDto> {
        this.logger.assign({ userId: identity.userId });

        const token = await this.authService.login(identity.userId);

        return AuthLoginResponseDto.fromResponse(token);
    }

    @ApiHeader({
        name: 'Authorization',
        description: 'Contains MS identity token',
    })
    @Post('logout')
    @UseGuards(IdentityGuard)
    @IgnoreLogging(IgnoreLoggingOptions.REQ_USER)
    @ApiResponse({
        status: 200,
        description: "Removes user's auth collection from db",
    })
    async logout(@Identity() identity: IdentityModel): Promise<void> {
        this.logger.assign({ userId: identity.userId });

        await this.authService.logout(identity.userId);

        this.logger.info(`Logout user. UserId: ${identity.userId}`);
    }

    @ApiHeader({
        name: 'Authorization',
        description: 'Contains JWT token',
    })
    @Get('user-details')
    @UseGuards(JwtGuard)
    @ApiForbiddenResponse()
    async getUserInfo(@Auth() auth: AuthReadModel): Promise<AuthUserDetailsResponseDto> {
        this.logger.assign({ userId: auth.userId, domain: auth.egnyte.domain });

        try {
            // TODO add to docs info about matching userId and domain/email under this endpoint
            const egnyteUserInfo = await this.egnyteAuthService.getEgUserInfo();

            this.logger.info(
                `EgnyteAPI get user info success - email: ${egnyteUserInfo.email} and domain: ${auth.egnyte.domain}`
            );

            return AuthUserDetailsResponseDto.fromResponse(egnyteUserInfo);
        } catch (error) {
            this.logger.error(`Not authorized to Egnyte. Error: ${inspect(error)}`);

            switch (error.status) {
                case 401: {
                    throw new ForbiddenException('Not authorized to Egnyte');
                }
                case 410: {
                    this.logger.info(`Request to egnyte failed, removing user data, ${error.message}`);
                    await this.authService.logout(auth.userId);
                    await this.authService.removeMsAuthSetup(auth.userId);
                    throw new ForbiddenException('Request to egnyte failed, removing user data');
                }
                default: {
                    throw error;
                }
            }
        }
    }

    private async handleGettingMsCodeError(query: AuthCodeQueryDto): Promise<AuthRedirectResponseDto> {
        switch (query.error) {
            case 'login_required': {
                const userId = query.state;

                this.logger.assign({ userId });
                this.logger.warn(`MS auth finish error - login_required: ${inspect(query)}`);

                let authSetupRead: AuthSetupReadModel;

                try {
                    authSetupRead = await this.authService.getAuthSetup(userId);
                } catch (error) {
                    const params = new URLSearchParams({
                        error: error instanceof GoneException ? 'Authorization process expired' : error.message,
                    });

                    const errorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

                    return AuthRedirectResponseDto.fromData(302, errorUrl);
                }

                const redirectUri = this.appRelative.fullyQualified('/auth/ms-auth-finish');

                const url = this.msAuthService.getRedirectToMsAuthorize({
                    state: query.state,
                    redirectUri,
                    loginHint: authSetupRead.msEmail,
                    prompt: 'login',
                });

                return AuthRedirectResponseDto.fromData(302, url);
            }
            default: {
                this.logger.warn(`MS auth finish error: ${inspect(query)}`);

                const params = new URLSearchParams({
                    error: query.error,
                });

                const initErrorUrl = this.appRelative.fullyQualifiedClient(`/loginConfirmation?${params.toString()}`);

                return AuthRedirectResponseDto.fromData(302, initErrorUrl);
            }
        }
    }
}
