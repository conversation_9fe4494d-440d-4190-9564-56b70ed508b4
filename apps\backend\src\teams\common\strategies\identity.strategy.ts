import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { BearerStrategy, IBearerStrategyOptionWithRequest, ITokenPayload } from 'passport-azure-ad';
import { ClsService } from 'nestjs-cls';
import { IdentityReadModel } from '../models/identity-read.model';

@Injectable()
export class IdentityStrategy extends PassportStrategy(BearerStrategy, 'azure-ad') {
    private static prepareOptions(configService: ConfigService): IBearerStrategyOptionWithRequest {
        return {
            identityMetadata: 'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',
            clientID: configService.get('msClientId'),
            audience: configService.get('msClientAppUri'),
            loggingLevel: 'warn',
            validateIssuer: false,
            passReqToCallback: false,
        };
    }

    constructor(
        configService: ConfigService,
        private clsService: ClsService
    ) {
        super(IdentityStrategy.prepareOptions(configService));
    }

    async validate(authInfo: ITokenPayload): Promise<IdentityReadModel> {
        const identity: IdentityReadModel = {
            userId: Buffer.from(`${authInfo.tid}_${authInfo.oid}`).toString('base64'),
            msUserId: authInfo.oid,
            msTenantId: authInfo.tid,
            ...(authInfo.preferred_username && { msEmail: authInfo.preferred_username }),
        };
        this.clsService.set('identity', identity);

        return identity;
    }
}
