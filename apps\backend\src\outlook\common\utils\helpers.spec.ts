import { Helpers } from './helpers';
import { SHARE_LINK_EXPIRATION_UNIT } from '../../share-link/enums/share-link-expiration-unit.enum';

describe('Helpers', () => {
    describe('getNameFromPath', () => {
        it('folder path', async () => {
            expect(Helpers.getNameFromPath('/Shared/outlook/outlookupload/direct')).toBe('direct');
        });
        it('file path', async () => {
            expect(Helpers.getNameFromPath('/Shared/outlook/outlookupload/direct/file.txt')).toBe('file.txt');
        });
    });

    describe('calculateFutureDate', () => {
        const expirationDateModel = {
            isActive: true,
            unitType: SHARE_LINK_EXPIRATION_UNIT.DAYS,
            value: 10,
        };
        beforeAll(() => {
            const mockDate = new Date('2024-11-10');
            jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
        });
        afterAll(() => {
            jest.restoreAllMocks();
        });

        it('should return null', async () => {
            expect(
                Helpers.calculateFutureDate({
                    ...expirationDateModel,
                    isActive: false,
                })
            ).toBe(null);
        });
        it('should add days to future date', async () => {
            expect(Helpers.calculateFutureDate(expirationDateModel)).toBe('2024-11-20');
        });
        it('should add weeks to future date', async () => {
            expect(
                Helpers.calculateFutureDate({
                    ...expirationDateModel,
                    unitType: SHARE_LINK_EXPIRATION_UNIT.WEEKS,
                })
            ).toBe('2025-01-29');
        });
        it('should add months to future date', async () => {
            expect(
                Helpers.calculateFutureDate({
                    ...expirationDateModel,
                    unitType: SHARE_LINK_EXPIRATION_UNIT.MONTHS,
                })
            ).toBe('2025-11-29');
        });
    });
});
