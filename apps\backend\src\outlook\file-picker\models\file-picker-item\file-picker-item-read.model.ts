import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';
import { IEgnyteSdkItem, IEgnyteSdkResult } from '../../../common/interfaces/egnyte-provider.interface';

export class FilePickerItemReadModel {
    @ValidateArgs()
    public static fromItemWithPath(data: IEgnyteSdkItem): FilePickerItemReadModel {
        return {
            name: FilePickerItemReadModel.extractNameFromPath(data),
            path: data.path,
            isFolder: !!data.folder_id,
            ...(data.folder_id ? { folderId: data.folder_id } : { fileId: data.group_id }),
            ...(data.parent_id && { parentId: data.parent_id }),
        };
    }

    @ValidateArgs()
    public static fromSDKItem(data: IEgnyteSdkItem): FilePickerItemReadModel {
        return {
            name: data.name,
            path: data.path,
            isFolder: !!data.is_folder,
            ...(data.is_folder ? { folderId: data.folder_id } : { fileId: data.group_id }),
            ...(data.parent_id && { parentId: data.parent_id }),
        };
    }

    @ValidateArgs()
    public static fromSDKResult(data: IEgnyteSdkResult): FilePickerItemReadModel {
        let items: FilePickerItemReadModel[];
        if (data.files || data.folders) {
            const { folders = [], files = [] } = data;
            items = [...folders, ...files].map((item) => FilePickerItemReadModel.fromSDKItem(item));
        }

        return {
            name: data.name,
            isFolder: true,
            path: data.path,
            folderId: data.folder_id,
            ...(data.parent_id && { parentId: data.parent_id }),
            ...(items && { items }),
        };
    }

    private static extractNameFromPath(item: IEgnyteSdkItem): string {
        return item.path.split('/').pop();
    }

    public name: string;

    public isFolder: boolean;

    public path: string;

    public folderId?: string;

    public fileId?: string;

    public parentId?: string;

    public items?: FilePickerItemReadModel[];
}
