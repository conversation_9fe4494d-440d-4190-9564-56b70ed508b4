import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { DatabaseUploadModule, DatabaseSettingsModule, DatabaseFileUploadModule } from 'ms-metaos-database/outlook';
import { UploadController } from './controllers/upload.controller';
import { UploadService } from './services/upload.service';
import { BULL_QUEUE } from '../common/consts/bull-queue.const';
import { MsGraphServiceModule } from '../common/services/ms-graph/ms-graph.service.module';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            configKey: 'outlook',
            name: BULL_QUEUE.ATTACHMENT,
        }),
        MsGraphServiceModule,
        EgnyteClientServiceModule,
        DatabaseUploadModule,
        DatabaseSettingsModule,
        DatabaseFileUploadModule,
    ],
    controllers: [UploadController],
    providers: [UploadService],
})
export class UploadModule {}
