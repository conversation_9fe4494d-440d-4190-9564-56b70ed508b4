/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types,func-names,no-param-reassign */
import { InternalServerErrorException } from '@nestjs/common';

export function ValidateArgs() {
    return (target: any, key: string | symbol, descriptor: PropertyDescriptor): PropertyDescriptor => {
        const original = descriptor.value;
        descriptor.value = function (...args) {
            args.forEach((arg, index) => {
                if (!arg) {
                    throw new InternalServerErrorException(
                        `Mapping error: ${target.name}.${String(key)} for argument with index ${index}`
                    );
                }
            });

            return original.apply(this, args);
        };

        return descriptor;
    };
}
