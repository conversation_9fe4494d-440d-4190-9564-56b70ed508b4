/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { ShareLinkPermissionsReadModel } from '../models/share-link-permissions/share-link-permissions-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class ShareLinkPermissionsResultNestedDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkPermissionsReadModel): ShareLinkPermissionsResultNestedDto {
        return {
            linkTypes: data.permissions.linkTypes,
        };
    }

    @ApiProperty({
        type: 'object',
        additionalProperties: { type: 'boolean' },
        example: { anyone: true, domain: true, direct: false },
    })
    public linkTypes: Partial<{
        [key: string]: boolean;
    }>;
}
