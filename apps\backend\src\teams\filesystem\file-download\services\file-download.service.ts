import { Injectable } from '@nestjs/common';
import { EgnyteService } from '../../../common/services/egnyte/egnyte.service';
import { ReportingService } from '../../../common/metrics/services/reporting.service';

@Injectable()
export class FileDownloadService {
    constructor(
        private egnyteService: EgnyteService,
        private reportingService: ReportingService
    ) {}

    public async downloadFile(groupId: string) {
        const file = await this.egnyteService.downloadFile(groupId);

        this.reportingService.processEvent({
            action: 'downloadFile',
            fileId: groupId,
            statusCode: 200,
        });

        return file;
    }
}
