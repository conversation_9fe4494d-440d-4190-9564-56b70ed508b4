import { FilePickerItemReadModel } from './file-picker-item-read.model';

export function getFilePickerItemReadModelMock({
    name = 'testItemName',
    isFolder = false,
    folderId = 'testFolderId1',
    fileId = 'testFileId1',
    path = '/Shared/testFolder/testFolderNested',
    parentId = undefined,
    withChildren = false,
} = {}): FilePickerItemReadModel {
    return {
        name,
        isFolder,
        path,
        ...(isFolder ? { folderId } : { fileId }),
        ...(parentId && { parentId }),
        ...(withChildren && {
            items: [
                getFilePickerItemReadModelMock({
                    name: 'testNestedItemName1',
                    fileId: 'testNestedFileId1',
                    parentId: folderId,
                }),
                getFilePickerItemReadModelMock({
                    name: 'testNestedItemName2',
                    fileId: 'testNestedFileId2',
                    parentId: folderId,
                }),
                getFilePickerItemReadModelMock({
                    isFolder: true,
                    name: 'testNestedItemName3',
                    folderId: 'testNestedFolderId3',
                    parentId: folderId,
                }),
            ],
        }),
    };
}
