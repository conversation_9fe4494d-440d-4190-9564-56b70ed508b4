import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import Busboy, { FileInfo } from 'busboy';
import { PinoLogger } from 'nestjs-pino';
import { inspect } from 'util';
import { FileUploadDataModel } from 'ms-metaos-database/outlook';

@Injectable()
export class UploadInterceptor implements NestInterceptor {
    constructor(private readonly logger: PinoLogger) {}

    async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<unknown>> {
        const request = context.switchToHttp().getRequest<Request & { fileStream: FileUploadDataModel }>();
        const multipart = Busboy({ headers: request.headers });
        request.pipe(multipart);

        await new Promise<void>((resolve, reject) => {
            multipart.on('file', (fieldName: string, file: NodeJS.ReadableStream, fileInfo: FileInfo) => {
                this.logger.info(`File upload process - Received file ${JSON.stringify(fileInfo)} as stream`);
                request.fileStream = FileUploadDataModel.fromData(fileInfo, file);
                resolve();
            });
            multipart.on('error', (error: unknown) => {
                this.logger.warn(`File upload process - Received error ${inspect(error)}`);
                request.unpipe(multipart);
                reject(error);
            });
        });

        return next.handle();
    }
}
