import crypto from 'crypto';
import { ForbiddenException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { ClsService } from 'nestjs-cls';
import { AuthReadModel, AuthSetupReadModel, AuthRepository, AuthSetupRepository } from 'ms-metaos-database/teams';
import { HttpErrorByCode } from '@nestjs/common/utils/http-error-by-code.util';
import { httpsifyDomain, sanitizeDomain } from '../../utils/sanitize';
import {
    Env,
    IShareLink,
    IShareLinkCreateProps,
    IShareLinkListProps,
    IShareLinkListResult,
    IShareLinkPermissionsResult,
    ISignature,
} from './egnyte-private-client.types';
import { DOMAIN_SIGNATURE_VALIDATION_PUBLIC_KEY, GET_ENV_TIMEOUT, QA_DCS } from './egnyte-private-client.consts';

@Injectable()
export class EgnytePrivateClientService {
    constructor(
        private logger: PinoLogger,
        private configService: ConfigService,
        private authRepository: AuthRepository,
        private authSetupRepository: AuthSetupRepository,
        private clsService: ClsService
    ) {}

    public getInstance(auth?: AuthReadModel | AuthSetupReadModel): EgnytePrivateInstance {
        return new EgnytePrivateInstance(
            auth || this.clsService.get<AuthReadModel>('auth'),
            this.logger,
            this.configService,
            this.authRepository,
            this.authSetupRepository
        );
    }
}

class EgnytePrivateInstance {
    constructor(
        private auth: AuthReadModel | AuthSetupReadModel,
        private logger: PinoLogger,
        private configService: ConfigService,
        private authRepository: AuthRepository,
        private authSetupRepository: AuthSetupRepository
    ) {}

    public async checkFileLinkAvailability(entryIds: string[]): Promise<IShareLinkPermissionsResult> {
        const data: IShareLinkPermissionsResult = await this.makeRequest(
            `https://${this.auth.egDomain}/rest/public/1.0/ms-teams/links/file/availability`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryIds),
            }
        );

        return data;
    }

    public async createLink({ lastEntryId, sourceId, shareLinkType }: IShareLinkCreateProps): Promise<IShareLink> {
        const data: IShareLink = await this.makeRequest(
            `https://${this.auth.egDomain}/rest/public/1.0/ms-teams/links/file`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    entry_id: lastEntryId,
                    channel_id: sourceId,
                    access: shareLinkType,
                }),
            }
        );

        return data;
    }

    public async getLinksForChannel({
        sourceId,
        limit = 100,
        offset = 0,
        sortBy = 'SHARE_DATE',
        sortDirection = 'DESC',
    }: IShareLinkListProps): Promise<IShareLinkListResult> {
        const data: IShareLinkListResult = await this.makeRequest(
            `https://${this.auth.egDomain}/rest/public/1.0/ms-teams/links`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    channel_id: sourceId,
                    limit,
                    offset,
                    sort_by: sortBy,
                    sort_direction: sortDirection,
                }),
            }
        );

        return data;
    }

    public async getEgPrivateToken(): Promise<string> {
        const { egDomain, egAccessToken } = this.auth;

        let egPrivateToken: string;
        const domain = sanitizeDomain(egDomain);

        try {
            const response = await fetch(`https://${domain}/rest/public/token-exchange/ms-teams`, {
                headers: {
                    'X-Mashery-Passed-Client-Id': this.configService.get('definitionApiKey'),
                    Authorization: `Bearer ${egAccessToken}`,
                },
            });

            if (!response.ok) {
                this.logger.info('[EgnytePrivateClient/getEgPrivateToken] token exchange failed');
                throw new Error('Token exchange request error');
            }

            const result = await response.json();
            if (!result.token) {
                this.logger.info(
                    '[EgnytePrivateClient/getEgPrivateToken] token exchange failed - lack of private token in response'
                );
                throw new Error('Private token not found');
            }
            egPrivateToken = result.token;
        } catch (e) {
            this.logger.error(e, '[EgnytePrivateClient/getEgPrivateToken] error');
            throw new InternalServerErrorException(e.message ?? 'Something went wrong');
        }

        return egPrivateToken;
    }

    public async getCFSToken(): Promise<string> {
        const { egDomain, egAccessToken } = this.auth;
        const url = `https://${egDomain}/rest/public/token-exchange/open-link-page`;
        try {
            const response = await fetch(url, {
                headers: {
                    'X-Mashery-Passed-Client-Id': this.configService.get('definitionApiKey'),
                    Authorization: `Bearer ${egAccessToken}`,
                },
            });

            if (!response.ok) throw new HttpErrorByCode[response.status](response.statusText);

            const result = await response.json();
            if (!result.token) {
                this.logger.info(
                    '[EgnytePrivateClient/getCFSToken] token exchange failed - lack of private token in response'
                );
                throw new Error('Private token not found');
            }

            return result.token;
        } catch (error) {
            this.logger.error(error, '[EgnytePrivateClient/getCFSToken] getting error');
            if (error.statusCode || error.response?.statusCode === 403) {
                throw new ForbiddenException('Egnyte token has expired');
            }
            throw new InternalServerErrorException(error.message ?? 'Cannot exchange token');
        }
    }

    private parseOptions(options: RequestInit, token: string): RequestInit {
        return {
            ...options,
            headers: {
                ...options.headers,
                Authorization: `msteams ${token}`,
            },
        };
    }

    private isAuthReadModel(model: AuthReadModel | AuthSetupReadModel): model is AuthReadModel {
        return 'userId' in model;
    }

    private isAuthSetupReadModel(model: AuthReadModel | AuthSetupReadModel): model is AuthSetupReadModel {
        return 'id' in model;
    }

    private async makeRequest<TResponse = unknown>(url: string, options: RequestInit): Promise<TResponse> {
        const requestOptions = this.parseOptions(options, this.auth.egPrivateToken);
        let response = await fetch(url, requestOptions);

        if (!response.ok) {
            if (response.status === 401) {
                this.logger.info('[EgnytePrivateClient/makeRequest] Fetch data failed. Refreshing private token');

                const newEgPrivateToken = await this.getEgPrivateToken();

                try {
                    if (this.isAuthReadModel(this.auth)) {
                        await this.authRepository.updateAuth(this.auth.userId, {
                            egPrivateToken: newEgPrivateToken,
                            egDomain: this.auth.egDomain,
                        });
                    } else if (this.isAuthSetupReadModel(this.auth)) {
                        await this.authSetupRepository.updateAuthSetup(this.auth.id, {
                            egPrivateToken: newEgPrivateToken,
                        });
                    }
                } catch (e) {
                    this.logger.error(
                        e,
                        '[EgnytePrivateClient/makeRequest] Update document with refreshed egPrivateToken failed.'
                    );
                }

                const responseNew = await fetch(url, this.parseOptions(options, newEgPrivateToken));

                if (!responseNew.ok) {
                    const responseText = await responseNew.text();

                    throw new InternalServerErrorException(responseText || 'Something went wrong');
                }

                response = responseNew;
            } else {
                this.logger.info('[EgnytePrivateClient/makeRequest] fetch data failed');
                const responseText = await response.text();

                throw new InternalServerErrorException(responseText || 'Something went wrong');
            }
        }

        const result = await response.json();
        this.logger.info('EgnytePrivateClient/makeRequest] fetch data success');

        return result;
    }

    private isEnv(env: any): env is Env {
        return 'workgroup' in env && 'analytics' in env;
    }

    public async getEnv(domain: string): Promise<Env | null> {
        const abortController = new AbortController();
        const abortTimeout = setTimeout(() => {
            abortController.abort();
        }, GET_ENV_TIMEOUT);

        const newDomain = httpsifyDomain(domain);
        try {
            const response = await fetch(`${newDomain}/rest/public/1.0/env-pub`, {
                redirect: 'follow',
                signal: abortController.signal,
            });
            clearTimeout(abortTimeout);

            if (response.status !== 200) {
                const error = await response.text().catch(() => undefined);
                if (error && error.message) {
                    throw Error(`[getEnv] Provided domain: ${domain}. Error message: ${error.message}`);
                }
                throw Error(`Could not get info for provided domain: ${sanitizeDomain(domain)} from Egnyte pubAPI`);
            }

            const resp = await response.json();
            if (response && this.isEnv(resp)) {
                return resp;
            }

            return null;
        } catch (e) {
            if (e.cause?.code === 'ENOTFOUND') {
                throw Error(`request to ${newDomain}/rest/public/1.0/env-pub failed, reason: ${e.cause.message}`);
            }
            throw e;
        }
    }

    public async verifyDomainSignature({ domain }: { domain: string }): Promise<ISignature> {
        this.logger.debug(`Domain signature validation in progress`);
        let result;
        try {
            result = await fetch(`${domain}/rest/public/1.0/workgroup/signature?revision=1`, {
                method: 'GET',
            });
        } catch (e) {
            if (e.cause?.code === 'ENOTFOUND') {
                throw Error(
                    `request to ${domain}/rest/public/1.0/workgroup/signature?revision=1 failed, reason: ${e.cause.message}`
                );
            }
            throw e;
        }

        if (result.status !== 200) {
            throw Error(`Could not get info for provided domain: ${sanitizeDomain(domain)} from Egnyte pubAPI`);
        }

        if (!result.ok) {
            const message = await result.text();
            this.logger?.error(message);
            throw Error(message);
        }

        const signatureResponse: ISignature = await result.json();

        if (signatureResponse.subject.expiration * 1000 < Date.now()) {
            this.logger?.error('Domain signature expired');
            throw Error('Domain signature expired');
        }

        const verifier = crypto.createVerify('sha384');
        verifier.write(JSON.stringify(signatureResponse.subject));
        verifier.end();

        const verificationResult = verifier.verify(
            DOMAIN_SIGNATURE_VALIDATION_PUBLIC_KEY,
            Buffer.from(signatureResponse.signature, 'base64')
        );

        if (!verificationResult) {
            this.logger?.error('Invalid domain signature');
            throw Error('Invalid domain signature');
        }

        this.logger.debug(`Domain signature validation end for ${domain}. Valid: ${verificationResult}`);

        return signatureResponse;
    }

    public async getValidDomainRecordFromInput(domain: string): Promise<{ domain: string }> {
        const response = { domain: '' };

        try {
            let newDomain = domain.trim();
            if (newDomain.includes('@')) {
                throw Error(`Attempted to provide email (${domain}) instead of a valid Egnyte domain`);
            }
            if (!newDomain.includes('.')) {
                newDomain += `.${this.configService.get('envDomainBaseURL') || 'egnyte.com'}`;
            }

            response.domain = httpsifyDomain(newDomain);
            response.domain = new URL(response.domain).origin;

            const {
                subject: { url },
            } = await this.verifyDomainSignature({ domain: response.domain });
            const verifiedDomain = httpsifyDomain(url);

            const env = await this.getEnv(verifiedDomain);

            if (this.isEnv(env)) {
                if (!this.configService.get('envDomainBaseURL')) {
                    this.logger?.info(
                        '[getValidDomainRecordFromInput] - no envDomainBaseURL provided from config file'
                    );
                }

                response.domain = `https://${env.workgroup.name}.${this.configService.get('envDomainBaseURL') || 'egnyte.com'}`;

                if (QA_DCS.includes(env.analytics.dcName)) {
                    // it's a specific QA environment -> custom domain
                    response.domain = `https://${env.workgroup.name}.qa-egnyte.com`;
                }
            }
        } catch (err) {
            throw Error(`[getValidDomainRecordFromInput] Error. ${err?.message || 'Error message not available'}`);
        }

        this.logger?.info(
            `[getValidDomainRecordFromInput] - provided value: ${domain}; result domain: ${response.domain}`
        );

        return response;
    }

    public async getEgDomainFromUrl(url: string): Promise<string> {
        const domain = new URL(httpsifyDomain(url)).hostname;

        if (domain.endsWith(this.configService.get('egDomainBaseUrl'))) return domain;

        return new URL((await this.getValidDomainRecordFromInput(url)).domain).hostname;
    }
}
