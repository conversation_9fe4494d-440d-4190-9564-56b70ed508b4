import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsString } from '@nestjs/class-validator';
import { BadRequestException } from '@nestjs/common';

enum DTO_ATTACHMENT_TYPE {
    EMAIL = 'email',
    ATTACHMENT = 'attachment',
}

export class UploadAttachmentsAttachmentNestedDto {
    @ApiProperty()
    @IsString()
    public readonly msAttachmentId: string;

    @ApiProperty()
    @IsString()
    @Transform(transformFileName, { toClassOnly: true })
    public readonly fileName: string;

    @ApiProperty()
    @IsEnum(DTO_ATTACHMENT_TYPE)
    public readonly egAttachmentType: DTO_ATTACHMENT_TYPE;
}

function transformFileName({ value, obj }: { value: string; obj: UploadAttachmentsAttachmentNestedDto }): string {
    switch (obj.egAttachmentType) {
        case DTO_ATTACHMENT_TYPE.EMAIL:
            return /\.eml$/.test(value) ? value : `${value}.eml`;
        case DTO_ATTACHMENT_TYPE.ATTACHMENT:
            return value;
        default:
            throw new BadRequestException(`Upload type ${obj.egAttachmentType} is not supported`);
    }
}
