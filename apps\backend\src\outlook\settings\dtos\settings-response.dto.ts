import { ApiProperty } from '@nestjs/swagger';
import { SettingsReadModel } from 'ms-metaos-database/outlook';
import { SettingsEgUploadFolderResponseNestedDto } from './settings-eg-upload-folder-response-nested.dto';
import { SettingsEgMetadataResponseNestedDto } from './settings-eg-metadata-response-nested.dto';

export class SettingsResponseDto {
    public static fromResponse(data: SettingsReadModel): SettingsResponseDto {
        return {
            ...(data.egUploadFolder && {
                egUploadFolder: SettingsEgUploadFolderResponseNestedDto.fromResponse(data.egUploadFolder),
            }),
            egMetadata: SettingsEgMetadataResponseNestedDto.fromResponse(data.egMetadata),
        };
    }

    @ApiProperty({ type: SettingsEgUploadFolderResponseNestedDto })
    public egUploadFolder: SettingsEgUploadFolderResponseNestedDto;

    @ApiProperty({ type: SettingsEgMetadataResponseNestedDto })
    public egMetadata: SettingsEgMetadataResponseNestedDto;
}
