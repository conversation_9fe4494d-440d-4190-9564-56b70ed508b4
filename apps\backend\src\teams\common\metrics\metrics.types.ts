import express from 'express';

export interface ReportMetadata {
    domain: string;
    userId: string | 'n/a';
    username: string;
    userAgent: string;
    ip: string | string[];
    requestId: string | string[] | 'n/a';
    tags: string | string[] | 'n/a';
}

export interface MetricsMiddlewareFactory {
    getMiddleware: () => (req: express.Request, res: express.Response, next: express.NextFunction) => void;
}
