import { ApiProperty } from '@nestjs/swagger';
import { UserDetailsModel } from '../../common/models/user-details/user-details.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class ShareLinkRecipientsNestedDto {
    @ValidateArgs()
    public static fromResponse(data: UserDetailsModel): ShareLinkRecipientsNestedDto {
        return {
            email: data.email,
            name: `${data.firstName} ${data.lastName}`,
            initials: `${data.firstName[0]}${data.lastName[0]}`,
        };
    }

    @ApiProperty()
    public email: string;

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public initials: string;
}
