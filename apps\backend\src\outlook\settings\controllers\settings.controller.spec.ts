import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import {
    getAuthReadModelMock,
    getCreateNamespaceModelMock,
    getSettingsReadModelMock,
    getSettingsUpdateModelMock,
    SettingsPostRequestDto,
} from 'ms-metaos-database/outlook';
import { SettingsController } from './settings.controller';
import { SettingsService } from '../services/settings.service';
import { getSettingsServiceMock } from '../services/settings.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { EG_USER_TYPE } from '../../common/consts/egnyte-user-type.const';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { SettingsPatchRequestDto } from '../dtos/settings-patch-request.dto';

describe('SettingsController', () => {
    let testingModule: TestingModule;
    let settingsService: SettingsService;
    let settingsController: SettingsController;

    beforeEach(async () => {
        jest.resetAllMocks();

        testingModule = await Test.createTestingModule({
            providers: [
                { provide: PinoLogger, useValue: getLoggerMock() },
                {
                    provide: SettingsService,
                    useValue: getSettingsServiceMock(),
                },
            ],
            controllers: [SettingsController],
        }).compile();

        settingsService = testingModule.get<SettingsService>(SettingsService);
        settingsController = testingModule.get<SettingsController>(SettingsController);
    });

    describe('getSettings', () => {
        it('should get settings with all possible keys', async () => {
            const expected = getSettingsReadModelMock();
            const result = await settingsController.getSettings(getAuthReadModelMock());

            expect(settingsService.getSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
            expect(result).toEqual(expected);
        });

        it('should get settings without any key', async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );

            const expected = getSettingsReadModelMock({ withRequiredOnly: true });
            const result = await settingsController.getSettings(getAuthReadModelMock());

            expect(settingsService.getSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
            expect(result).toEqual(expected);
        });
    });

    describe('patchSettings', () => {
        it('should patch settings with all possible keys and get response with all possible keys', async () => {
            const expected = getSettingsReadModelMock();
            const dto: SettingsPatchRequestDto = {
                egUploadFolderId: UNIT_TEST_VARS.settings.uploadFolder.folderId,
                egMetadata: {
                    isNamespaceAdded: UNIT_TEST_VARS.settings.metadata.isNamespaceAdded,
                    isSavingEnabled: UNIT_TEST_VARS.settings.metadata.isSavingEnabled,
                },
            };

            const result = await settingsController.patchSettings(getAuthReadModelMock(), dto);

            expect(settingsService.updateCommonSettings).toHaveBeenCalledWith(
                UNIT_TEST_VARS.userId,
                getSettingsUpdateModelMock()
            );
            expect(result).toEqual(expected);
        });

        it('should patch settings as admin without any key and get response without not required keys', async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );
            const expected = getSettingsReadModelMock({ withRequiredOnly: true });
            const result = await settingsController.patchSettings(getAuthReadModelMock(), {});

            expect(settingsService.updateCommonSettings).toHaveBeenCalledWith(UNIT_TEST_VARS.userId, {});
            expect(result).toEqual(expected);
        });

        it('should patch settings as power user without any key and get response without not required keys', async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );
            const powerUserAuthData = getAuthReadModelMock();
            powerUserAuthData.egnyte.userType = EG_USER_TYPE.POWER;

            const expected = getSettingsReadModelMock({ withRequiredOnly: true });
            const result = await settingsController.patchSettings(powerUserAuthData, {});

            expect(settingsService.updateCommonSettings).toHaveBeenCalledWith(powerUserAuthData.userId, {});
            expect(settingsService.createNamespace).not.toHaveBeenCalled();
            expect(result).toEqual(expected);
        });
    });

    describe('createNamespace', () => {
        it(`power uses shouldn't be able to create a namespace`, async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );
            const powerUserAuthData = getAuthReadModelMock();
            powerUserAuthData.egnyte.userType = EG_USER_TYPE.POWER;

            try {
                await settingsController.createNamespace(powerUserAuthData, {
                    egMetadata: {
                        isNamespaceAdded: true,
                        isSavingEnabled: true,
                    },
                });
            } catch (error) {
                expect(error.message).toEqual('Only admin can create namespace');
                expect(error.status).toEqual(403);
                expect(error.response.error).toEqual('Forbidden');
            }
        });

        it('should create namespace  with all possible keys and get response with all possible keys', async () => {
            const expected = getSettingsReadModelMock();
            const dto: SettingsPostRequestDto = {
                egMetadata: {
                    isNamespaceAdded: UNIT_TEST_VARS.settings.metadata.isNamespaceAdded,
                    isSavingEnabled: UNIT_TEST_VARS.settings.metadata.isSavingEnabled,
                },
            };

            const result = await settingsController.createNamespace(getAuthReadModelMock(), dto);

            expect(settingsService.updateCommonSettings).toHaveBeenCalledWith(
                UNIT_TEST_VARS.userId,
                getCreateNamespaceModelMock()
            );
            expect(result).toEqual(expected);
        });
    });

    describe('removeNamespace', () => {
        it(`power uses shouldn't be able to remove a namespace`, async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );
            const powerUserAuthData = getAuthReadModelMock();
            powerUserAuthData.egnyte.userType = EG_USER_TYPE.POWER;

            try {
                await settingsController.removeNamespace(powerUserAuthData);
            } catch (error) {
                expect(error.message).toEqual('Only admin can remove namespace');
                expect(error.status).toEqual(403);
                expect(error.response.error).toEqual('Forbidden');
            }
        });

        it('should remove namespace', async () => {
            (settingsService.getSettings as jest.Mock).mockResolvedValue(
                getSettingsReadModelMock({ withRequiredOnly: true })
            );
            const powerUserAuthData = getAuthReadModelMock();
            await settingsController.removeNamespace(powerUserAuthData);

            expect(settingsService.removeMetadataNamespace).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
        });
    });
});
