import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { LoggerModule } from 'nestjs-pino';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ServeStaticModule } from '@nestjs/serve-static';
import { DbConnection } from 'ms-metaos-types/types';
import { BullModule } from '@nestjs/bullmq';
import { AuthModule } from './auth/auth.module';
import {
    configBullModule,
    configConfigModule,
    configLoggerModule,
    configMongooseModule,
    configServeStaticModule,
} from './common/config/module-config';
import { FilePickerModule } from './file-picker/file-picker.module';
import { ShareLinkModule } from './share-link/share-link.module';
import { UploadLinkModule } from './upload-link/upload-link.module';
import { JwtStrategy } from './common/strategies/jwt.strategy';
import { UploadModule } from './upload/upload.module';
import { MonitoringModule } from './common/monitoring/monitoring.module';
import { SettingsModule } from './settings/settings.module';
import { ReportingMiddleware } from './common/monitoring/reporting.middleware';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

@Module({
    imports: [
        MonitoringModule,
        BullModule.forRootAsync('outlook', configBullModule()),
        ConfigModule.forRoot(configConfigModule()),
        MongooseModule.forRootAsync(configMongooseModule(DbConnection.Outlook)),
        LoggerModule.forRootAsync(configLoggerModule()),
        ServeStaticModule.forRootAsync(configServeStaticModule()),
        TerminusModule,
        AuthModule,
        FilePickerModule,
        ShareLinkModule,
        UploadLinkModule,
        UploadModule,
        SettingsModule,
    ],
    providers: [JwtStrategy, LoggingInterceptor],
})
export class OutlookModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer.apply(ReportingMiddleware).forRoutes({ path: 'outlook/*', method: RequestMethod.ALL });
    }
}
