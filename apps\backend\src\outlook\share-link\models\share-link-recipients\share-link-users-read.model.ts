import { AuthReadModel } from 'ms-metaos-database/outlook';
import { PaginationModel } from '../../../common/models/pagination/pagination.model';
import { UserDetailsModel } from '../../../common/models/user-details/user-details.model';
import { IEgnyteSearchUsersResult } from '../../../common/interfaces/egnyte-provider.interface';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class ShareLinkUsersReadModel extends PaginationModel<UserDetailsModel> {
    @ValidateArgs()
    public static fromResponse(data: IEgnyteSearchUsersResult, auth: AuthReadModel): ShareLinkUsersReadModel {
        return {
            count: data.resources.length,
            items: data.resources.map((item) => UserDetailsModel.fromSearchResponse(item, auth)),
            offset: data.startIndex,
            total: data.totalResults,
        };
    }
}
