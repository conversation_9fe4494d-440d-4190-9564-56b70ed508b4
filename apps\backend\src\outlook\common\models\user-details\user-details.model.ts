import { AuthReadModel } from 'ms-metaos-database/outlook';
import { EG_USER_TYPE } from '../../consts/egnyte-user-type.const';
import { IEgnyteSearchUserResult, IEgnyteUserInfo } from '../../interfaces/egnyte-provider.interface';
import { Mappers } from '../../utils/mappers';
import { ValidateArgs } from '../../decorators/validate-args.decorator';

export class UserDetailsModel {
    @ValidateArgs()
    public static fromAuthResponse(data: IEgnyteUserInfo, auth: AuthReadModel): UserDetailsModel {
        return {
            id: data.id,
            username: data.username,
            email: data.email,
            firstName: data.first_name,
            lastName: data.last_name,
            userType: Mappers.mapRawToModelUserType(data.user_type),
            domain: auth.egnyte.domain,
        };
    }

    @ValidateArgs()
    public static fromSearchResponse(data: IEgnyteSearchUserResult, auth: AuthReadModel): UserDetailsModel {
        return {
            id: data.id,
            username: data.userName,
            email: data.email,
            firstName: data.name.givenName,
            lastName: data.name.familyName,
            userType: Mappers.mapRawToModelUserType(data.userType),
            domain: auth.egnyte.domain,
            emailChangePending: data.emailChangePending,
            active: data.active,
        };
    }

    public id: number;

    public username: string;

    public email: string;

    public firstName: string;

    public lastName: string;

    public userType: EG_USER_TYPE;

    public domain: string;

    public active?: boolean;

    public emailChangePending?: boolean;
}
