import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, ValidateNested } from '@nestjs/class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ShareLinkExpirationNestedDto } from './share-link-expiration-nested.dto';
import { ShareLinkItemNestedDto } from './share-link-item-nested.dto';

export class ShareLinkRequestDto {
    @ApiProperty({ type: ShareLinkItemNestedDto, isArray: true })
    @IsNotEmpty()
    @Type(() => ShareLinkItemNestedDto)
    @ValidateNested()
    public items: ShareLinkItemNestedDto[];

    @ApiProperty({ type: String, enum: ['anyone', 'domain', 'direct', 'recipients', 'password'], example: 'anyone' })
    @IsNotEmpty()
    @IsString()
    public shareType: 'anyone' | 'domain' | 'direct' | 'recipients' | 'password';

    @ApiProperty({ type: ShareLinkExpirationNestedDto })
    @Type(() => ShareLinkExpirationNestedDto)
    @ValidateNested()
    public expiration: ShareLinkExpirationNestedDto;

    @ApiPropertyOptional({ type: String, isArray: true })
    @IsOptional()
    @IsString({ each: true })
    public recipients?: string[];

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    public password?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    public sendEmail?: boolean;
}
