import { DomainEnvMetadata } from './domain-env-metadata';
import { IPublic } from './public.interface';
import { UNIT_TEST_VARS } from './unit-test-vars';

export function getDomainEnvMetadataMock(): IPublic<DomainEnvMetadata> {
    return {
        getValidDomainRecordFromInput: jest.fn().mockResolvedValue(UNIT_TEST_VARS.egDomain),
        getDomainEnv: jest.fn().mockResolvedValue({
            workgroup: {
                timezone: {
                    offset: '+01:00',
                    displayName: 'Central European Standard Time',
                    id: 'Europe/Warsaw',
                },
            },
        }),
    };
}
