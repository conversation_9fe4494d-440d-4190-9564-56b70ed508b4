import { ForbiddenException, GoneException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PinoLogger } from 'nestjs-pino';
import {
    AuthCreateModel,
    AuthReadModel,
    AuthRepository,
    AuthSetupCreateModel,
    AuthSetupReadModel,
    AuthSetupRepository,
    AuthSetupUpdateModel,
} from 'ms-metaos-database/outlook';

@Injectable()
export class AuthService {
    constructor(
        private readonly authSetupRepository: AuthSetupRepository,
        private readonly authRepository: AuthRepository,
        private readonly jwtService: JwtService,
        private readonly logger: PinoLogger
    ) {}

    async login(userId: string): Promise<string> {
        const auth: AuthReadModel | null = await this.authRepository.findOneAuth(userId);

        if (!auth) {
            this.logger.info(`Auth not found. UserId: ${userId}`);
            throw new ForbiddenException(`Auth data not found`);
        }

        this.logger.assign({
            domain: auth?.egnyte?.domain || 'n/a',
            msTenantId: auth?.microsoft?.tenantId || 'n/a',
        });

        this.logger.info(`Auth found. UserId: ${userId}`);

        return this.jwtService.sign({
            userId: auth.userId,
        });
    }

    async logout(userId: string): Promise<void> {
        await this.authRepository.removeAuth(userId);
    }

    async getAuthSetup(userId: string): Promise<AuthSetupReadModel> {
        const authSetup = await this.authSetupRepository.getAuthSetup(userId);

        if (!authSetup) {
            this.logger.error(`AuthSetup expired. UserId: ${userId}`);

            throw new GoneException('AuthSetup expired');
        }

        this.logger.info(`AuthSetup found. UserId: ${userId}`);

        return authSetup;
    }

    async setupAuthData(authSetupCreate: AuthSetupCreateModel): Promise<AuthSetupReadModel> {
        const authSetupRead = await this.authSetupRepository.startAuthSetup(authSetupCreate);

        if (!authSetupRead) {
            this.logger.error(
                `Auth setup start failed - MS Outlook assertion. UserId: ${authSetupCreate.userId} and tenantId: ${authSetupCreate.tenantId}`
            );
            throw new InternalServerErrorException('Auth setup start failed - MS Outlook assertion');
        }

        this.logger.info(`Auth setup start success - MS Outlook assertion. UserId: ${authSetupCreate.userId}`);

        return authSetupRead;
    }

    async addMsAuthSetup(authSetupUpdate: AuthSetupUpdateModel): Promise<void> {
        const authSetup = await this.authSetupRepository.addMsAuth(authSetupUpdate);

        if (!authSetup) {
            this.logger.error(`Auth setup failed - MS authorization. UserId: ${authSetupUpdate.userId}`);

            throw new GoneException('AuthSetup expired');
        }

        this.logger.info(`Auth updated - MS authorization. UserId: ${authSetupUpdate.userId}`);
    }

    async removeMsAuthSetup(userId: string): Promise<void> {
        this.logger.info(`Removing user auth setup form DB, userId: ${userId}`);

        await this.authSetupRepository.removeAuthSetup(userId);
    }

    async addEgAuthSetup(authSetupUpdate: AuthSetupUpdateModel): Promise<void> {
        const authSetup = await this.authSetupRepository.addEgAuth(authSetupUpdate);

        if (!authSetup) {
            this.logger.error(
                `Auth setup failed - Egnyte authorization. UserId: ${authSetupUpdate.userId} and domain: ${authSetupUpdate.egDomain}`
            );

            throw new GoneException('AuthSetup expired');
        }

        this.logger.info(
            `Auth updated - Egnyte authorization. UserId: ${authSetupUpdate.userId} and domain: ${authSetupUpdate.egDomain}`
        );
    }

    async finalizeAuth(userId: string): Promise<AuthReadModel> {
        const authSetup = await this.authSetupRepository.getAuthSetup(userId);

        if (!authSetup) {
            this.logger.error(`Finalize auth failed - authSetup expired. UserId: ${userId}`);

            throw new GoneException('Finalize auth failed - authSetup expired');
        }

        const authCreateModel = AuthCreateModel.fromAuthSetupModel(authSetup);
        const auth = await this.authRepository.addAuth(authCreateModel);

        this.logger.info(`Finalized auth. UserId: ${userId}`);

        return auth;
    }

    public async getAuth(userId: string): Promise<AuthReadModel> {
        const authReadModel: AuthReadModel | null = await this.authRepository.findOneAuth(userId);

        if (!authReadModel) {
            this.logger.warn(`User not found. UserId: ${userId}`);
            throw new ForbiddenException('User not found');
        }

        return authReadModel;
    }
}
