import { Module } from '@nestjs/common';
import { UploadLinkController } from './controllers/upload-link.controller';
import { UploadLinkService } from './services/upload-link.service';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';
import { AuthModule } from '../auth/auth.module';

@Module({
    imports: [AuthModule, EgnyteClientServiceModule],
    controllers: [UploadLinkController],
    providers: [UploadLinkService],
})
export class UploadLinkModule {}
