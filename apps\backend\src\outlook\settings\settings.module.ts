import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseSettingsModule } from 'ms-metaos-database/outlook';
import { SettingsController } from './controllers/settings.controller';
import { EgnyteClientServiceModule } from '../common/services/egnyte-client/egnyte-client.service.module';
import { SettingsService } from './services/settings.service';

@Module({
    imports: [DatabaseSettingsModule, EgnyteClientServiceModule],
    controllers: [SettingsController],
    providers: [SettingsService],
})
export class SettingsModule {}
