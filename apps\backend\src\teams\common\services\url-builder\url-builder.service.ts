import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UrlBuilderService {
    constructor(private configService: ConfigService) {}

    public getFullClientPath() {
        const domain = this.configService.get('domain');
        const basePath = this.configService.get('basePath');
        const clientPath = this.configService.get('teamsClientPath');

        return `${domain}${basePath}${clientPath}`;
    }
}
