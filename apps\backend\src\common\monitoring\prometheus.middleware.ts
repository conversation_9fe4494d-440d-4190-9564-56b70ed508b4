import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as express from 'express';
import { PrometheusService } from './prometheus.service';

@Injectable()
export class PrometheusMiddleware implements NestMiddleware {
    private readonly prometheusMiddleware: (
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
    ) => void;

    constructor(configService: ConfigService, prometheusService: PrometheusService) {
        if (configService.get('prometheus.enabled')) {
            this.prometheusMiddleware = prometheusService.getMiddleware();
        }
    }

    public use(req: express.Request, res: express.Response, next: express.NextFunction): void {
        if (this.prometheusMiddleware) {
            this.prometheusMiddleware(req, res, next);
        }
    }
}
