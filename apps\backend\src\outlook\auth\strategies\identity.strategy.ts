import { PassportStrategy } from '@nestjs/passport';
import { BearerStrategy, IBearerStrategyOptionWithRequest, ITokenPayload } from 'passport-azure-ad';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

type IdentityPayload = {
    userId: string;
    msUserId: string;
    tenantId: string;
    msEmail?: string;
};

@Injectable()
export class IdentityStrategy extends PassportStrategy(BearerStrategy, 'outlook-azure-ad') {
    constructor(configService: ConfigService) {
        super(IdentityStrategy.prepareOptions(configService));
    }

    validate(authInfo: ITokenPayload): IdentityPayload {
        const payload = {
            userId: Buffer.from(`${authInfo.tid}_${authInfo.oid}`).toString('base64'),
            msUserId: authInfo.oid,
            tenantId: authInfo.tid,
            ...(authInfo.preferred_username && { msEmail: authInfo.preferred_username }),
        };

        return payload;
    }

    private static prepareOptions(configService: ConfigService): IBearerStrategyOptionWithRequest {
        return {
            identityMetadata: 'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',
            clientID: configService.get('msClientId'),
            loggingLevel: 'warn',
            validateIssuer: false,
            passReqToCallback: false,
        };
    }
}
