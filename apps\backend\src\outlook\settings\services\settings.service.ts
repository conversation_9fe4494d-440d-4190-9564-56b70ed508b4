import { Injectable } from '@nestjs/common';
import { Pi<PERSON>Logger } from 'nestjs-pino';
import { inspect } from 'util';
import { ConfigService } from '@nestjs/config';
import { SettingsReadModel, SettingsRepository, SettingsUpdateModel, AuthReadModel } from 'ms-metaos-database/outlook';
import { ReportingActions } from '../../common/services/reporting/reporting.enums';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { IEgnyteMetadataNamespaceCreateModel } from '../../common/interfaces/egnyte-metadata-namespace-model.interface';
import { FilePickerItemReadModel } from '../../file-picker/models/file-picker-item/file-picker-item-read.model';

@Injectable()
export class SettingsService {
    constructor(
        private readonly settingsRepository: SettingsRepository,
        private readonly egnyteClientService: EgnyteClientService,
        private readonly reportingService: ReportingService,
        private readonly configService: ConfigService,
        private readonly logger: PinoLogger
    ) {}

    public async getSettings(userId: string): Promise<SettingsReadModel> {
        let rawSettings = await this.settingsRepository.getSettings(userId);

        try {
            if (rawSettings.egUploadFolder) {
                const egUploadFolderDetails = await this.getEgUploadFolderDetails(rawSettings.egUploadFolder.folderId);
                rawSettings.egUploadFolder.path = egUploadFolderDetails.path;
                rawSettings.egUploadFolder.name = egUploadFolderDetails.name;
            }
        } catch (e) {
            this.logger.warn(`Getting settings error. UserId ${userId}. Error: ${inspect(e)}`);

            rawSettings = await this.settingsRepository.deleteSettingsKey(userId, 'egUploadFolderId');
        }

        try {
            const namespaceName = this.configService.get<string>('egnyteMetadataNamespaceName');
            rawSettings.egMetadata.isNamespaceAdded = await this.isMetadataNamespaceAdded(namespaceName);
        } catch (e) {
            this.logger.warn(`Getting settings error. UserId ${userId}. Error: ${inspect(e)}`);

            rawSettings.egMetadata.isNamespaceAdded = false;
        }

        this.logger.info(
            `Received settings - egUploadFolder path: ${rawSettings.egUploadFolder?.path}. Folder id: ${rawSettings.egUploadFolder?.folderId}. UserId: ${userId}`
        );

        return rawSettings;
    }

    public async updateCommonSettings(userId: string, updateModel: SettingsUpdateModel): Promise<void> {
        await this.settingsRepository.updateSettings(userId, updateModel);
    }

    public async createNamespace(authData: AuthReadModel): Promise<void> {
        const namespaceName = this.configService.get<string>('egnyteMetadataNamespaceName');
        const namespaceAlreadyAdded = await this.isMetadataNamespaceAdded(namespaceName);

        if (namespaceAlreadyAdded) {
            this.logger.info(`Metadata namespace already exists. UserId: ${authData.userId}`);

            return;
        }

        const newNamespaceData: IEgnyteMetadataNamespaceCreateModel = {
            name: namespaceName,
            displayName: 'Outlook Add-in',
            scope: 'protected',
            keys: {
                from: {
                    type: 'string',
                    displayName: 'From',
                },
                to: {
                    type: 'string',
                    displayName: 'To',
                },
                subject: {
                    type: 'string',
                    displayName: 'Subject',
                },
                'attachments-number': {
                    type: 'integer',
                    displayName: 'Number of attachments',
                },
                timestamp: {
                    type: 'date',
                    displayName: 'Delivery date',
                },
            },
        };

        await this.egnyteClientService.addMetadataNamespace(newNamespaceData);

        this.reportingService.processEvent({
            action: ReportingActions.ADDED_NAMESPACE,
            domain: authData.egnyte.domain,
            username: authData.egnyte.username,
            userId: authData.egnyte.id,
            statusCode: 200,
        });

        this.logger.info(`Metadata namespace added. UserId: ${authData.userId}`);
    }

    private async getEgUploadFolderDetails(folderId: string): Promise<FilePickerItemReadModel> {
        return this.egnyteClientService.getFolderDetailsById(folderId, { withChildren: false });
    }

    private async isMetadataNamespaceAdded(namespaceName: string): Promise<boolean> {
        const result = await this.egnyteClientService.getMetadataNamespaces();

        return result.some((metadataNamespace) => metadataNamespace.name === namespaceName);
    }

    public async removeMetadataNamespace(userId: string): Promise<void> {
        await this.updateCommonSettings(userId, { egMetadata: { isSavingEnabled: false } });

        const namespaceName = this.configService.get<string>('egnyteMetadataNamespaceName');
        await this.egnyteClientService.removeMetadataNamespace(namespaceName);
    }
}
