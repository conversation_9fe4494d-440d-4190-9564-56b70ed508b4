/* eslint-disable camelcase */

export interface IPintLinkDomainSettings {
    file: {
        linkTypes: {
            anyone?: boolean;
            domain?: boolean;
            direct?: boolean;
            recipients?: boolean;
            password?: boolean;
        };
        default: 'anyone' | 'domain' | 'direct' | 'password' | null;
    };
    folder: {
        linkTypes: {
            anyone?: boolean;
            domain?: boolean;
            direct?: boolean;
            recipients?: boolean;
            password?: boolean;
        };
        default: 'anyone' | 'domain' | 'direct' | 'password' | null;
    };
    expiration: {
        default: {
            value: number;
            unit: 'days' | 'week' | 'months' | 'click';
        } | null;
        max_allowed: {
            expiry_time_value: number;
            expiry_time_unit: 'days' | 'week' | 'months';
            expiry_clicks: number;
        } | null;
        upload_links: boolean;
    };
}
