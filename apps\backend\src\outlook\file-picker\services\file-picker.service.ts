import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { FilePickerPageReadModel } from '../models/file-picker-page/file-picker-page-read.model';
import { FilePickerItemReadModel } from '../models/file-picker-item/file-picker-item-read.model';
import { FilePickerBreadcrumbsReadModel } from '../models/file-picker-breadcrumbs/file-picker-breadcrumbs-read.model';
import { FilePickerInfoOptionsModel } from '../models/file-picker-item/file-picker-info-options.model';
import { FilePickerSearchQueryDto } from '../dtos/file-picker-search-query.dto';

@Injectable()
export class FilePickerService {
    constructor(
        private readonly egnyteClientService: EgnyteClientService,
        private readonly logger: PinoLogger
    ) {}

    public async getDataByFolderId(id: string, options: FilePickerInfoOptionsModel): Promise<FilePickerItemReadModel> {
        return this.egnyteClientService.getFolderDetailsById(id, options);
    }

    public async getDataByFolderPath(
        path: string,
        options: FilePickerInfoOptionsModel
    ): Promise<FilePickerItemReadModel> {
        return this.egnyteClientService.getFolderDetailsByPath(path, options);
    }

    public async getBookmarkedItems(): Promise<FilePickerPageReadModel> {
        return this.egnyteClientService.getBookmarkedItems();
    }

    public async searchForItems(searchQuery: FilePickerSearchQueryDto): Promise<FilePickerPageReadModel> {
        return this.egnyteClientService.searchForItems(searchQuery);
    }

    public async getRecentItems(): Promise<FilePickerPageReadModel> {
        return this.egnyteClientService.getRecentItems();
    }

    public async getBreadcrumbsByFolderId(folderId: string): Promise<FilePickerBreadcrumbsReadModel> {
        const folderBreadcrumbs = [];
        let parentFolderId = folderId;

        this.logger.info(`Building breadcrumbs for folder ${folderId} start`);
        while (parentFolderId) {
            // eslint-disable-next-line no-await-in-loop
            const result = await this.egnyteClientService.getFolderDetailsById(parentFolderId, { withChildren: false });

            folderBreadcrumbs.push(result);

            parentFolderId = result.parentId;
        }

        this.logger.info(
            `Building breadcrumbs for folder ${folderId} finish. Number of breadcrumbs: ${
                folderBreadcrumbs.length
            }. Breadcrumb ids: ${folderBreadcrumbs.map((breadcrumb) => breadcrumb.folderId).join(', ')}`
        );

        return FilePickerBreadcrumbsReadModel.fromData(
            folderId,
            folderBreadcrumbs[0].path,
            folderBreadcrumbs.reverse()
        );
    }
}
