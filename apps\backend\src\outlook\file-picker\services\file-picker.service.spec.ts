import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import { FilePickerService } from './file-picker.service';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { getEgnyteClientServiceMock } from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getFilePickerPageReadModelMock } from '../models/file-picker-page/file-picker-page-read.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { getFilePickerItemReadModelMock } from '../models/file-picker-item/file-picker-item-read.mock';
import { getFilePickerBreadcrumbsReadModelMock } from '../models/file-picker-breadcrumbs/file-picker-breadcrumbs-read.mock';

describe('FilePickerService', () => {
    let testingModule: TestingModule;
    let egnyteClientService: EgnyteClientService;
    let logger: PinoLogger;
    let filePickerService: FilePickerService;

    beforeEach(async () => {
        jest.resetAllMocks();

        testingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: EgnyteClientService,
                    useValue: getEgnyteClientServiceMock(),
                },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                FilePickerService,
            ],
        }).compile();

        egnyteClientService = testingModule.get<EgnyteClientService>(EgnyteClientService);
        logger = testingModule.get<PinoLogger>(PinoLogger);
        filePickerService = testingModule.get<FilePickerService>(FilePickerService);
    });

    describe('getBookmarkedItems', () => {
        it.todo('Create getBookmarkedItems method tests');
    });

    describe('getDataByFolderId', () => {
        it.todo('Create getDataByFolderId method tests');
    });

    describe('getDataByFolderPath', () => {
        it.todo('Create getDataByFolderPath method tests');
    });

    describe('searchForItems', () => {
        it('should successfully return mapped search results', async () => {
            const expected = getFilePickerPageReadModelMock();
            const result = await filePickerService.searchForItems(UNIT_TEST_VARS.searchAllQuery);

            expect(egnyteClientService.searchForItems).toHaveBeenCalledWith(UNIT_TEST_VARS.searchAllQuery);
            expect(result).toEqual(expected);
        });
    });

    describe('getRecentItems', () => {
        it('should successfully return mapped 10 recent updated files', async () => {
            const expected = getFilePickerPageReadModelMock({ itemsType: 'files' });
            const result = await filePickerService.getRecentItems();

            expect(egnyteClientService.getRecentItems).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });
    });

    describe('getBreadcrumbsByFolderId', () => {
        it('should successfully build breadcrumbs for folder', async () => {
            const currentItem = getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsCurrentItem',
                name: 'testBreadcrumbsCurrentItem',
                path: '/testBreadcrumbsParentItem/testBreadcrumbsCurrentItem',
                parentId: 'testBreadcrumbsParentItem',
            });
            const parentItem = getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsParentItem',
                name: 'testBreadcrumbsParentItem',
                path: '/testBreadcrumbsParentItem',
                parentId: 'testBreadcrumbsRootItem',
            });
            const rootItem = getFilePickerItemReadModelMock({
                isFolder: true,
                folderId: 'testBreadcrumbsRootItem',
                name: 'testBreadcrumbsRootItem',
                path: '/',
            });

            (egnyteClientService.getFolderDetailsById as jest.Mock)
                .mockResolvedValueOnce(currentItem)
                .mockResolvedValueOnce(parentItem)
                .mockResolvedValue(rootItem);

            const expected = getFilePickerBreadcrumbsReadModelMock();
            const result = await filePickerService.getBreadcrumbsByFolderId('testBreadcrumbsCurrentItem');

            expect(logger.info).toHaveBeenCalledTimes(2);
            expect(egnyteClientService.getFolderDetailsById).toHaveBeenCalledTimes(3);
            expect(result).toEqual(expected);
        });
    });
});
