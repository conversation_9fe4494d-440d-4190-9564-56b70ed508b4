import { ApiProperty } from '@nestjs/swagger';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { UserDetailsModel } from '../../common/models/user-details/user-details.model';

export class AuthUserDetailsResponseDto {
    @ValidateArgs()
    public static fromResponse(data: UserDetailsModel): AuthUserDetailsResponseDto {
        return {
            egUsername: data.username,
            egEmail: data.email,
            egFirstName: data.firstName,
            egLastName: data.lastName,
            egUserType: data.userType,
            egDomain: data.domain,
        };
    }

    @ApiProperty()
    public egUsername: string;

    @ApiProperty()
    public egEmail: string;

    @ApiProperty()
    public egFirstName: string;

    @ApiProperty()
    public egLastName: string;

    @ApiProperty()
    public egUserType: string;

    @ApiProperty()
    public egDomain: string;
}
