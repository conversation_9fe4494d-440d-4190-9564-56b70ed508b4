interface IEgnyteMetadataKeyCreateModel {
    type: 'integer' | 'string' | 'decimal' | 'date' | 'enum';
    displayName?: string;
    priority?: number;
    helpText?: string;
    data?: string[];
}

export interface IEgnyteMetadataNamespaceCreateModel {
    name: string;
    scope: 'private' | 'public' | 'protected';
    keys: {
        [key: string]: IEgnyteMetadataKeyCreateModel;
    };
    displayName?: string;
    priority?: number;
}

export interface IEgnyteMetadataNamespaceUpdateModel {
    name: string;
    keys: {
        [key: string]: number | string;
    };
}
