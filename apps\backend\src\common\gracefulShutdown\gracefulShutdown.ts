import { Injectable, OnApplicationShutdown } from '@nestjs/common';
import { <PERSON><PERSON>Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GracefulShutdownService implements OnApplicationShutdown {
    constructor(
        private logger: Pi<PERSON>Logger,
        private readonly configService: ConfigService
    ) {}

    async onApplicationShutdown(signal: string) {
        this.logger.info(`onApplicationShutdown received shutdown signal: ${signal}`);
        if (signal === 'SIGTERM') {
            const shutdownDelay = this.configService.get('gracefulShutdownDelayMs');
            const startTime = Date.now();

            this.logger.info('Got SIGTERM. Graceful shutdown start');

            if (shutdownDelay > 30 * 1000) {
                // 30 sec
                // infrastructure limit for shutdown delay: 30 sec | 5 mins
                // checkout terminationGracePeriodSeconds in kubernetes
                this.logger.warn(
                    `Warning! Graceful shutdown delay is set to ${shutdownDelay}ms. Check infrastructure limitation for delaying app shutdowns`
                );
            }

            try {
                // add here more advanced clean up
                this.logger.info(
                    `Graceful shutdown finished stop function successfully; time ${Date.now() - startTime} ms`
                );

                await sleep(shutdownDelay);
                // due to process.exit(0) instant execution
                // it is likely this log will not be available in the Kibana or any other log dashboard
                // although it could be available inside the pod console
                // reason for that is to have two shutdown mechanisms
                // one inside app (node) and second on kubernetes level
                this.logger.info(
                    `[unpredictable log] Graceful shutdown is triggering process exit with code 0; time ${
                        Date.now() - startTime
                    } ms`
                );

                await sleep(100);
                process.exit(0);
            } catch (error) {
                if (error instanceof Error) {
                    this.logger.error(error, `Graceful shutdown failed; time ${Date.now() - startTime} ms`);
                }
                setTimeout(() => process.exit(1), 1);
            }
        }
    }
}

function sleep(ms: number) {
    return new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
}
