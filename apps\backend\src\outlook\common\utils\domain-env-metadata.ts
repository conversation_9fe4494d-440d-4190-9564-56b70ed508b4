import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { Helpers } from './helpers';

type Timezone = {
    offset: string;
    displayName: string;
    id: string;
};

type Workgroup = {
    timezone: Timezone;
};

type DomainEnv = {
    workgroup: Workgroup;
};

@Injectable()
export class DomainEnvMetadata {
    private readonly GET_ENV_TIMEOUT = 40 * 1000;

    constructor(private httpService: HttpService) {}

    private async getEnv(domainRaw: string): Promise<DomainEnv> {
        const domain = Helpers.httpsifyDomain(domainRaw);

        const response = await firstValueFrom(
            this.httpService.get<DomainEnv>(`${domain}/rest/public/1.0/env-pub`, {
                timeout: this.GET_ENV_TIMEOUT,
            })
        );

        return response.data;
    }

    private buildDomainFromDomainName(domainName: string, domainBaseUrl: string): string {
        return `https://${domainName}.${domainBaseUrl}`;
    }

    async getValidDomainRecordFromInput(domainName: string, domainBaseUrl: string): Promise<string> {
        try {
            const validatedDomainName = Helpers.validateDomainName(domainName);

            const domain = this.buildDomainFromDomainName(validatedDomainName, domainBaseUrl);

            await this.getEnv(domain);

            return domain;
        } catch (err) {
            throw Error('Not a valid Egnyte domain');
        }
    }

    async getDomainEnv(egDomain: string): Promise<DomainEnv> {
        try {
            return this.getEnv(egDomain);
        } catch (err) {
            throw Error('Not a valid Egnyte domain');
        }
    }
}
