/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { ShareLinkPermissionsReadModel } from '../models/share-link-permissions/share-link-permissions-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class ShareLinkPermissionsDefaultsNestedDto {
    @ValidateArgs()
    public static fromResponse(data: ShareLinkPermissionsReadModel): ShareLinkPermissionsDefaultsNestedDto {
        return {
            linkTypes: data.defaults.linkTypes,
            default: data.defaults.default,
        };
    }

    @ApiProperty({
        type: 'object',
        additionalProperties: { type: 'boolean' },
        example: { anyone: true, domain: true, direct: false },
    })
    public linkTypes: Partial<{
        [key: string]: boolean;
    }>;

    @ApiProperty({ type: String, enum: ['anyone', 'password', 'domain', 'direct', 'recipients'], nullable: true })
    public default: string | null;
}
