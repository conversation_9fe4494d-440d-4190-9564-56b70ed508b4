import { ApiProperty } from '@nestjs/swagger';
import { DB_UPLOAD_STATUS } from 'ms-metaos-database/outlook';

export class UploadAttachmentsResponseDto {
    public static fromData(data: { uploadId: string }): UploadAttachmentsResponseDto {
        return { status: DB_UPLOAD_STATUS.IN_PROGRESS, uploadId: data.uploadId };
    }

    @ApiProperty()
    public readonly status: DB_UPLOAD_STATUS;

    @ApiProperty()
    public readonly uploadId: string;
}
