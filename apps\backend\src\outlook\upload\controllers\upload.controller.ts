import {
    Body,
    Controller,
    Delete,
    Get,
    NotFoundException,
    Param,
    Post,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { <PERSON>pi<PERSON>eader, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import {
    AuthReadModel,
    FileUploadCreateModel,
    FileUploadDataModel,
    FileUploadUpdateModel,
    UploadCreateModel,
} from 'ms-metaos-database/outlook';
import { Auth } from '../../common/decorators/auth.decorator';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { UploadAttachmentsResponseDto } from '../dtos/upload-attachments-response.dto';
import { UploadService } from '../services/upload.service';
import { UploadAttachmentsRequestDto } from '../dtos/upload-attachments-request.dto';
import { UploadStatusResponseDto } from '../dtos/upload-status-response.dto';
import { IgnoreLogging, IgnoreLoggingOptions } from '../../common/decorators/ignore-logging.decorator';
import { UPLOAD_STATUS } from '../enums/upload-status.enum';
import { UploadFileResponseDto } from '../dtos/upload-file-response.dto';
import { UploadFileAbortRequestDto } from '../dtos/upload-file-abort-request.dto';
import { UploadFileQueryDto } from '../dtos/upload-file-query.dto';
import { UploadInterceptor } from '../interceptors/upload.interceptor';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { ReportingActions } from '../../common/services/reporting/reporting.enums';

@ApiTags('UploadController')
@ApiHeader({
    name: 'Authorization',
    description: 'Contains JWT token',
})
@Controller('outlook/upload')
@UseGuards(JwtGuard)
export class UploadController {
    constructor(
        private readonly logger: PinoLogger,
        private readonly uploadService: UploadService,
        private readonly reportingService: ReportingService
    ) {}

    @ApiOkResponse({ type: UploadFileResponseDto })
    @Post('/file')
    @UseInterceptors(UploadInterceptor)
    public async uploadFile(
        @Req() request: Request & { fileStream: FileUploadDataModel },
        @Auth() auth: AuthReadModel,
        @Query() fields: UploadFileQueryDto
    ): Promise<UploadFileResponseDto> {
        const fileUploadCreateModel = FileUploadCreateModel.fromDto(fields, auth);
        const file = request.fileStream;
        const fileName = Buffer.from(file.name, 'ascii').toString('utf8');

        const uploadStartTime = Date.now();
        const result = await this.uploadService.uploadFile(fileUploadCreateModel, file.stream, fileName);

        if (!result) {
            this.logger.warn(`File upload process - delete upload, time: ${(Date.now() - uploadStartTime) / 1000}s`);
            throw new NotFoundException(`The file has been deleted. UserId: ${auth.userId}`);
        }
        this.logger.info(`File upload process - upload successful, time: ${(Date.now() - uploadStartTime) / 1000}s`);
        this.reportingService.processEvent({
            action: ReportingActions.UPLOAD_AND_SHARE,
            domain: auth.egnyte.domain,
            username: auth.egnyte.username,
            userId: auth.egnyte.id,
            fileId: result.fileId || result.folderId,
            time: ReportingService.calculateOperationTime(uploadStartTime),
            statusCode: 200,
        });

        return UploadFileResponseDto.fromModel(result);
    }

    @ApiOkResponse()
    @Post('/file/abort')
    public async abortUpload(@Body() body: UploadFileAbortRequestDto, @Auth() auth: AuthReadModel): Promise<void> {
        const fileUploadUpdateModel = FileUploadUpdateModel.fromDto(body, auth);
        await this.uploadService.markFileToDelete(fileUploadUpdateModel);
    }

    @ApiOkResponse({ type: UploadAttachmentsResponseDto })
    @Post('/attachments')
    public async uploadAttachmentsStart(
        @Body() uploadData: UploadAttachmentsRequestDto,
        @Auth() auth: AuthReadModel
    ): Promise<UploadAttachmentsResponseDto> {
        const uploadId = uuidv4();
        this.logger.assign({ msMessageId: uploadData.message.msMessageId });
        const egUploadFolder = await this.uploadService.getUploadFolder(auth.userId);
        const models = await Promise.all(
            uploadData.attachments.map((attachment) =>
                UploadCreateModel.fromDto(uploadData, attachment, auth, egUploadFolder, uploadId)
            )
        );

        await this.uploadService.startUpload(models);

        return UploadAttachmentsResponseDto.fromData({ uploadId });
    }

    @ApiOkResponse({ type: UploadStatusResponseDto })
    @IgnoreLogging(IgnoreLoggingOptions.START_STOP)
    @Get('/attachments/:msMessageId')
    public async getAttachmentsStatus(
        @Param('msMessageId') msMessageId: string,
        @Query('uploadId') uploadId: string,
        @Auth() auth: AuthReadModel
    ): Promise<UploadStatusResponseDto> {
        this.logger.assign({ msMessageId });

        const response = await this.uploadService.checkAttachmentStatus(auth.userId, msMessageId, uploadId);

        const uploadsInProgress = response.filter((upload) => upload.status === UPLOAD_STATUS.IN_PROGRESS);

        if (uploadsInProgress.length === 0) {
            const failedUploads = response.filter((upload) => upload.status === UPLOAD_STATUS.ERROR);
            const qtyUploadErrors = failedUploads.length;

            if (qtyUploadErrors > 0) {
                this.logger.error(
                    `Summary upload files - with error: ${qtyUploadErrors}, with success: ${
                        response.length - qtyUploadErrors
                    }
                    . Upload folder: ${failedUploads[0].egPath}
                    . Details: ${failedUploads
                        .map(
                            (failedUpload) =>
                                `{ msFileName: ${failedUpload.msFileName}, reason: ${failedUpload.reason} }`
                        )
                        .join(', ')}`
                );
            }
        }

        return UploadStatusResponseDto.fromResponse(response, msMessageId);
    }

    @Delete('/attachments/:msMessageId')
    public async deleteAll(@Param('msMessageId') msMessageId: string, @Auth() auth: AuthReadModel): Promise<void> {
        this.logger.assign({ msMessageId });

        await this.uploadService.deleteUploads(auth.userId, msMessageId);
    }
}
