import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class AuthMSReadModel {
    @ValidateArgs()
    public static fromResponse(data: IGetTokenResponse): AuthMSReadModel {
        return {
            requestId: data.requestId,
            accessToken: data.accessToken,
            expireTime: data.expireTime,
            ...(data.refreshToken && { refreshToken: data.refreshToken }),
            ...(data.scopes && { scopes: data.scopes }),
        };
    }

    public accessToken: string;

    public expireTime: number;

    public refreshToken?: string;

    public scopes?: string[];

    public requestId: string;
}
