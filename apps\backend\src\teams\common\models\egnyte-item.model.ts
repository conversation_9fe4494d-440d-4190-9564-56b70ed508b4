import {
    ListFileRes,
    ListFolderRes,
    SearchFileRespondInterface,
    SearchFolderRespondInterface,
    ListRecentFilesInterface,
    SearchSummaryRespondInterface,
} from '@integrations/egnyte-ts-sdk';
import { IPublic } from '../utils/public.interface';

export class EgnyteItemReadModel {
    public static fromApiResponse(
        response:
            | ListFolderRes
            | ListFileRes
            | SearchFileRespondInterface
            | SearchFolderRespondInterface
            | SearchSummaryRespondInterface
    ): IPublic<EgnyteItemReadModel> {
        const items =
            response.is_folder === true
                ? [...((response as ListFolderRes).files ?? []), ...((response as ListFolderRes).folders ?? [])].map(
                      (item) => EgnyteItemReadModel.fromApiResponse(item as ListFolderRes | ListFileRes)
                  )
                : undefined;

        return {
            key:
                response.is_folder === true
                    ? (response as ListFolderRes).folder_id
                    : (response as ListFileRes).group_id,
            name: response.name,
            path: response.path,
            isFolder: response.is_folder,
            ...(response.is_folder === false && { lastEntryId: (response as ListFileRes).entry_id }),
            ...(response.is_folder === false && {
                lastModified: new Date((response as ListFileRes).last_modified).getTime(),
            }),
            ...(response.is_folder === true &&
                (response as ListFolderRes).total_count !== undefined && {
                    totalCount: (response as ListFolderRes).total_count,
                }),
            ...('size' in response && { size: response.size }),
            ...('snippet' in response && { snippet: response.snippet }),
            ...('content' in response && { content: response.content }),
            ...('summary' in response && { summary: response.summary }),
            ...('uploaded_by' in response && { uploadedBy: response.uploaded_by }),
            ...(items && { items }),
        };
    }

    public static isNotAShortcut(result: EgnyteItemReadModel) {
        return result.isFolder || !['egnyte_f', 'egnyte_d', 'egnyte_e'].includes(result.name.split('.').pop());
    }

    public static fromRecentFile(recentFile: ListRecentFilesInterface): IPublic<EgnyteItemReadModel> {
        return {
            key: recentFile.group_id,
            name: recentFile.name,
            path: recentFile.path,
            lastEntryId: recentFile.entry_id,
            lastModified: recentFile.last_modified,
            uploadedBy: recentFile.uploaded_by,
            isFolder: false,
        };
    }

    public key: string;

    public path: string;

    public name: string;

    public size?: number;

    public isFolder: boolean;

    public totalCount?: number;

    public lastEntryId?: string;

    public lastModified?: number;

    public uploadedBy?: string;

    public snippet?: string;

    public content?: string;

    public summary?: string;

    public items?: EgnyteItemReadModel[];
}
