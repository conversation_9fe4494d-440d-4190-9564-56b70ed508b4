import {
    BadRequestException,
    Controller,
    Get,
    InternalServerErrorException,
    Post,
    Query,
    Req,
    Res,
    UseGuards,
} from '@nestjs/common';
import { ApiCreatedResponse, ApiOkResponse, ApiResponse, ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import express from 'express';
import { ConfigService } from '@nestjs/config';
import { IGetTokenResponse } from '@integrations/pint-ms/lib/ms-auth/interfaces/ms-auth.interface';
import { UserEnum } from '@integrations/egnyte-ts-sdk';
import { AuthSetupReadModel, AuthReadModel, AdminAuthRepository, AuthSetupRepository } from 'ms-metaos-database/teams';
import { MicrosoftAuthClientService } from 'ms-metaos-modules';
import { IdentityGuard } from '../../common/guards/identity.guard';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { Identity } from '../../common/decorators/identity.decorator';
import { IdentityReadModel } from '../../common/models/identity-read.model';
import { AdminSetupInfoResponseDto } from '../dtos/responses/admin-setup-info-response.dto';
import { AuthService } from '../services/auth.service';
import { SetupResponseDto } from '../dtos/responses/setup-response.dto';
import { StartQueryDto } from '../dtos/queries/start-query.dto';
import { EgnyteService } from '../../common/services/egnyte/egnyte.service';
import { UrlBuilderService } from '../../common/services/url-builder/url-builder.service';
import { sanitizeDomain } from '../../common/utils/sanitize';
import { Auth } from '../../common/decorators/auth.decorator';
import { MsAuthFinishQueryDto } from '../dtos/queries/ms-auth-finish-query.dto';
import { EgAuthFinishQueryDto } from '../dtos/queries/eg-auth-finish-query.dto';
import { LoginHandlerResponseDto } from '../dtos/responses/login-handler-response.dto';
import { UserDetailsResponseDto } from '../dtos/responses/user-details-response.dto';
import { ReportingService } from '../../common/metrics/services/reporting.service';
import { OauthUserInfoQueryDto } from '../dtos/queries/oauth-user-info-query.dto';
import { ResourceSettingsService } from '../../tenant/resource-settings/services/resource-settings.service';
import { ExchangeAdminTokenResponseDto } from '../dtos/responses/exchange-admin-token-response.dto';
import { LoginQueryDto } from '../dtos/queries/login-query.dto';
import { BackendException } from '../../common/utils/BackendException';

@Controller('teams/user/auth')
@ApiTags('AuthController')
export class AuthController {
    constructor(
        private configService: ConfigService,
        private msAuthClientService: MicrosoftAuthClientService,
        private authService: AuthService,
        private egnyteService: EgnyteService,
        private reportingService: ReportingService,
        private adminAuthRepository: AdminAuthRepository,
        private authSetupRepository: AuthSetupRepository,
        private resourceSettingsService: ResourceSettingsService,
        private logger: PinoLogger,
        private urlBuilderService: UrlBuilderService
    ) {}

    @UseGuards(IdentityGuard)
    @Get('/admin-setup-info')
    @ApiOkResponse({
        description: 'Endpoint to check if the admin setup was performed successfully for this tenant',
        type: AdminSetupInfoResponseDto,
    })
    public async adminSetupInfoHandler(@Identity() identity: IdentityReadModel): Promise<AdminSetupInfoResponseDto> {
        const adminAuthModel = await this.authService.getAdminAuthByTenantId(identity.msTenantId);

        return AdminSetupInfoResponseDto.fromModel(adminAuthModel);
    }

    @UseGuards(IdentityGuard)
    @Post('/setup')
    @ApiCreatedResponse({ description: 'Endpoint to start the auth process', type: SetupResponseDto })
    public async setupHandler(
        @Req() request: express.Request,
        @Identity() identity: IdentityReadModel
    ): Promise<SetupResponseDto> {
        this.logger.info('[Auth/setup] setup handler start');
        const authorizationHeader = request.header('Authorization') as `Bearer ${string}`;

        let authSetup = await this.authService.addAuthSetup(identity);

        this.logger.info(
            {
                setupId: authSetup.id,
                msUserId: authSetup.msUserId,
            },
            '[Auth/setup] saved setup for user'
        );

        authSetup = await this.authService.exchangeIdentityToken({ authorizationHeader, authSetup });

        return {
            setupId: authSetup.id,
        };
    }

    @Get('/start')
    @ApiResponse({ status: 302 })
    public async startHandler(@Res() res: express.Response, @Query() query: StartQueryDto): Promise<void> {
        this.logger.info('[Auth/start] start handler query', query);

        const { setupId, multidomainContextDomain, multidomainContext, updateAdminToken } = query;

        const authSetup: AuthSetupReadModel = await this.authService.getAuthSetupById(setupId);

        if (!authSetup.msAccessToken) {
            this.logger.info('[Auth/start] Access token not found in authSetup');

            const redirectMsAuthFinishUri = `${this.configService.get('domain')}${this.configService.get(
                'basePath'
            )}/teams/user/auth/ms-auth-finish`;

            const redirectUrlToMsAuth = this.msAuthClientService.getRedirectToMsAuthorize({
                state: setupId,
                redirectUri: redirectMsAuthFinishUri,
                loginHint: authSetup.msEmail,
                prompt: authSetup?.options?.copilotContext ? '' : 'login',
            });

            return res.redirect(redirectUrlToMsAuth);
        }

        const redirectEgAuthFinishUri = `${this.configService.get('domain')}${this.configService.get(
            'basePath'
        )}/teams/user/auth/eg-auth-finish${updateAdminToken ? '?updateAdminToken=true' : ''}`;

        const adminAuth = await this.authService.findAdminAuthByTenantId(authSetup.msTenantId);

        if (!adminAuth && !authSetup?.options?.copilotContext && !multidomainContext) {
            throw new BadRequestException('AdminSetup not finished');
        }

        if (multidomainContext) {
            await this.authSetupRepository.updateAuthSetup(authSetup.id, {
                options: {
                    ...(authSetup.options || {}),
                    multidomainContext,
                },
            });
        }

        const domain = multidomainContext ? multidomainContextDomain : adminAuth && sanitizeDomain(adminAuth.egDomain);

        const params = new URLSearchParams({
            client_id: this.configService.get('definitionApiKey'),
            redirect_uri: redirectEgAuthFinishUri,
            state: encodeURIComponent(setupId),
            include_domain: 'true',
            response_type: 'code',
            scope: this.configService.get('tokenScope'),
            ...(domain && { domain }),
        });

        const egAuthUrl = `${this.configService.get('egAuthBaseUrl')}?${params.toString()}`;

        this.logger.info({ egAuthUrl }, '[Auth/start] redirecting straight to eg url');

        return res.redirect(egAuthUrl);
    }

    @Get('/ms-auth-finish')
    @ApiResponse({ status: 302 })
    public async msAuthFinishHandler(@Res() res: express.Response, @Query() query: MsAuthFinishQueryDto): Promise<any> {
        const { error, state: setupId, code: msAccessCode } = query;
        this.logger.info({ query }, '[Auth/msAuthFinish] query');

        if (error) {
            this.logger.info(error, '[Auth/msAuthFinish] error');
            throw new BadRequestException(error);
        }

        if (!setupId || !msAccessCode) {
            this.logger.info({ query }, '[AuthNext/msAuthFinish] invalid query params');
            throw new BadRequestException('Invalid auth data');
        }

        const authSetup = await this.authService.getAuthSetupById(setupId);

        const msGraphAuthResults: IGetTokenResponse = await this.msAuthClientService.handleAuthCode({
            code: msAccessCode,
            msTenantId: authSetup.msTenantId,
            scopes: this.configService.get('msScopes'),
            redirectUri: `${this.configService.get('domain')}${this.configService.get(
                'basePath'
            )}/teams/user/auth/ms-auth-finish`,
        });

        await this.authService.handleMsGraphAuthResult(setupId, msGraphAuthResults);
        this.logger.info('[Auth/msAuthFinish] msAuthSetupAdded');

        const redirectUri = `${this.configService.get('domain')}${this.configService.get(
            'basePath'
        )}/teams/user/auth/eg-auth-finish`;

        const params = new URLSearchParams({
            client_id: this.configService.get('definitionApiKey'),
            redirect_uri: redirectUri,
            state: encodeURIComponent(setupId),
            include_domain: 'true',
            response_type: 'code',
            scope: this.configService.get('tokenScope'),
        });

        const egAuthUrl = `${this.configService.get('egAuthBaseUrl')}?${params.toString()}`;

        this.logger.info({ egAuthUrl }, '[AuthNext/msAuthFinish] redirecting to url');

        return res.redirect(egAuthUrl);
    }

    @Get('/eg-auth-finish')
    @ApiResponse({ status: 302 })
    public async egAuthFinishHandler(@Res() res: express.Response, @Query() query: EgAuthFinishQueryDto): Promise<any> {
        const { state: setupId, updateAdminToken } = query;
        this.logger.info({ query }, '[Auth/egAuthFinish] request query');

        const { egDomain, egAccessToken } = await this.authService.handleEgAuthResponse({
            redirectUri: `${this.configService.get('domain')}${this.configService.get(
                'basePath'
            )}/teams/user/auth/eg-auth-finish${updateAdminToken ? '?updateAdminToken=true' : ''}`,
            error: query.error,
            domain: query.domain,
            code: query.code,
        });

        const authSetup = await this.authService.addEgAuthSetup({ setupId, egDomain, egAccessToken });
        const auth = await this.authService.finalizeAuth(setupId);
        const adminAuth = await this.adminAuthRepository.getByTenantId(authSetup.msTenantId);

        if (adminAuth && updateAdminToken) {
            const userInfo = await this.egnyteService.getUserInfoByCredentials({ egDomain, egAccessToken });

            if (userInfo.userType === UserEnum.ADMIN) {
                const { msTenantId } = await this.authService.getAuthSetupById(setupId);
                const adminAuth = await this.authService.getAdminAuthByTenantId(msTenantId);

                await this.adminAuthRepository.updateAdminAuth(adminAuth.id, {
                    egAccessToken,
                });
            }
        }

        this.logger.info(
            {
                userId: auth.userId,
            },
            '[Auth/egAuthFinish] Auth saved successfully'
        );

        const domain = sanitizeDomain(egDomain);

        const tags = [];
        if (authSetup?.options?.copilotContext) tags.push('copilot');
        if (authSetup?.options?.multidomainContext) {
            tags.push('multiDomain');
            if (adminAuth?.egDomain === domain) tags.push('primaryDomain');
        }

        this.reportingService.processEvent({
            action: 'login',
            statusCode: 200,
            tags: tags.join(','),
            // enhance reported data with logged in user data, which is not present in `req`.
            userId: auth.userId,
            domain,
            username: authSetup.egUsername || authSetup.msEmail || 'n/a',
        });

        this.logger.info(
            {
                userId: auth.userId,
            },
            '[Auth/egAuthFinish] rendering success auth template'
        );

        const statusQuery = new URLSearchParams({
            status: 'ok',
            domain: String(domain),
        });

        const clientPath = this.urlBuilderService.getFullClientPath();

        if (authSetup?.options?.copilotContext) {
            return res.redirect(`${clientPath}/auth-end-copilot?${statusQuery.toString()}`);
        }

        return res.redirect(`${clientPath}/auth-end?${statusQuery.toString()}`);
    }

    @UseGuards(IdentityGuard)
    @Get('/oauth-user-info')
    public async oauthUserDetailsHandler(
        @Identity() identity: IdentityReadModel,
        @Query() query: OauthUserInfoQueryDto
    ): Promise<{ isChannelOwner: boolean }> {
        const { msTeamId, msResourceId } = query;

        try {
            const isChannelOwner = await this.resourceSettingsService.isUserChannelOwner({
                auth: identity,
                msTeamId,
                msResourceId,
            });
            this.logger.info(`[oauthUserDetailsHandler] isChannelOwner: ${isChannelOwner}`);

            return {
                isChannelOwner,
            };
        } catch (error) {
            this.logger.error(`[oauthUserDetailsHandler] Error checking if user is the channel owner`, error);

            if (error instanceof BackendException) {
                throw error;
            }

            throw new InternalServerErrorException('Something went wrong');
        }
    }

    @UseGuards(IdentityGuard)
    @Post('/login')
    @ApiOkResponse({ type: LoginHandlerResponseDto })
    public async loginHandler(
        @Identity() identity: IdentityReadModel,
        @Query() loginQuery: LoginQueryDto
    ): Promise<LoginHandlerResponseDto> {
        this.logger.debug('[Auth/login] start handling');

        const { token, domain } = await this.authService.loginUser(identity, loginQuery);

        return {
            accessToken: token,
            domain,
        };
    }

    @UseGuards(IdentityGuard)
    @Post('/logout')
    @ApiOkResponse()
    public async logoutHandler(@Identity() identity: IdentityReadModel, @Query('domain') domain): Promise<void> {
        try {
            await this.authService.removeAuth(identity.userId, domain);
        } catch (e) {
            this.logger.error(e, '[Auth/logout] removeAuth error');
            throw new InternalServerErrorException('Cannot remove auth data');
        }
        this.reportingService.processEvent({
            action: 'logout',
            statusCode: 200,
        });

        this.logger.info({ userId: identity.userId }, '[Auth/logout] auth removed successfully');
    }

    @UseGuards(JwtGuard)
    @Get('/info')
    @ApiOkResponse({ type: UserDetailsResponseDto })
    public async userDetailsHandler(@Auth() auth: AuthReadModel): Promise<UserDetailsResponseDto> {
        const resultEnv = await this.authService.getDomainEnv(auth.egDomain);

        const { brandSettings } = resultEnv.workgroup;

        const currentUser = await this.authService.getCurrentEgUserData();
        const adminAuth = await this.adminAuthRepository.getByTenantId(auth.msTenantId);

        const isMainAdmin = adminAuth ? await this.egnyteService.isCurrentUserMainAdmin() : false;

        return {
            egBranding: {
                logo:
                    brandSettings.logoUrl ||
                    'https://storage.googleapis.com/pint-assets/ms-teams-addin/egnyte_logo_192px_production.png',
                color: brandSettings.themeSettings?.homeHeaderColor || '#28cec3',
                label: brandSettings.fileServerLabel || 'Egnyte for Teams',
            },
            egDomain: sanitizeDomain(auth.egDomain),
            egUser: {
                usernameFormatted: `${currentUser.firstName} ${currentUser.lastName}`,
                userType: currentUser.userType,
                userId: String(currentUser.id),
                username: currentUser.username,
                email: currentUser.email,
            },
            isMainAdmin,
        };
    }

    @UseGuards(JwtGuard)
    @Get('/admin-token-exchange')
    @ApiCreatedResponse({ type: ExchangeAdminTokenResponseDto })
    public async exchangeAdminToken(@Auth() user: AuthReadModel): Promise<ExchangeAdminTokenResponseDto> {
        return this.authService.exchangeAdminToken({ user });
    }
}
