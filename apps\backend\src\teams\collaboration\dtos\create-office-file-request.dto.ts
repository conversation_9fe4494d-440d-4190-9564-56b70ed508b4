import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from '@nestjs/class-validator';
import { EG_WOPI_APP_TYPE } from 'ms-metaos-types/enums';

export class CreateOfficeFileRequestDto {
    @ApiProperty({ enum: EG_WOPI_APP_TYPE, type: String, example: EG_WOPI_APP_TYPE.WORD })
    @IsEnum(EG_WOPI_APP_TYPE)
    public app: EG_WOPI_APP_TYPE;

    @ApiProperty()
    @IsString()
    public folderId: string;

    @ApiProperty()
    @IsString()
    public path: string;

    @ApiProperty()
    @IsString()
    public fileName: string;
}
