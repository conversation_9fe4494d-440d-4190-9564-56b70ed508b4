import { Test, TestingModule } from '@nestjs/testing';
import { PinoLogger } from 'nestjs-pino';
import { getQueueToken } from '@nestjs/bullmq';
import { utils } from '@integrations/pint-common';
import { Queue } from 'bullmq';
import { InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
    FileUploadRepository,
    getFileUploadRepositoryMock,
    getSettingsRepositoryMock,
    getUploadCreateModelMock,
    getUploadRepositoryMock,
    SettingsRepository,
    UploadCreateModel,
    UploadRepository,
} from 'ms-metaos-database/outlook';
import { UploadService } from './upload.service';
import { BULL_QUEUE } from '../../common/consts/bull-queue.const';
import { EgnyteClientService } from '../../common/services/egnyte-client/egnyte-client.service';
import { getEgnyteClientServiceMock } from '../../common/services/egnyte-client/egnyte-client.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getConfigServiceMock } from '../../common/config/configuration.mock';

describe('UploadService', () => {
    let testingModule: TestingModule;

    let attachmentQueue: Queue;
    let uploadRepository: UploadRepository;
    let logger: PinoLogger;
    let uploadService: UploadService;

    beforeEach(async () => {
        jest.resetAllMocks();

        jest.spyOn(utils.file, 'incrementFile')
            .mockReturnValueOnce('testFileItemId1 (1)')
            .mockReturnValueOnce('testFileItemId1 (2)')
            .mockReturnValue('testFileItemId1');

        testingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: getQueueToken(BULL_QUEUE.ATTACHMENT),
                    useValue: {
                        add: jest
                            .fn()
                            .mockImplementation((_, uploadData: UploadCreateModel) =>
                                Promise.resolve({ id: `testJob-${uploadData.msAttachmentId}` })
                            ),
                    },
                },
                {
                    provide: UploadRepository,
                    useValue: getUploadRepositoryMock(),
                },
                {
                    provide: EgnyteClientService,
                    useValue: getEgnyteClientServiceMock(),
                },
                {
                    provide: FileUploadRepository,
                    useValue: getFileUploadRepositoryMock(),
                },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                {
                    provide: ConfigService,
                    useValue: getConfigServiceMock(),
                },
                {
                    provide: SettingsRepository,
                    useValue: getSettingsRepositoryMock(),
                },
                UploadService,
            ],
        }).compile();

        attachmentQueue = testingModule.get<Queue>(getQueueToken(BULL_QUEUE.ATTACHMENT));
        uploadRepository = testingModule.get<UploadRepository>(UploadRepository);
        logger = testingModule.get<PinoLogger>(PinoLogger);
        uploadService = testingModule.get<UploadService>(UploadService);
    });

    describe('startUpload', () => {
        it('should successfully create jobs for all files', async () => {
            const uploadModels = [
                getUploadCreateModelMock({
                    msAttachmentId: 'testMsAttachmentId1',
                    msFileName: 'testFileName1',
                    egAttachmentType: 'email',
                }),
                getUploadCreateModelMock({
                    msAttachmentId: 'testMsAttachmentId2',
                    msFileName: 'testFileName2',
                    egAttachmentType: 'attachment',
                }),
                getUploadCreateModelMock({
                    msAttachmentId: 'testMsAttachmentId3',
                    msFileName: 'testFileName3',
                    egAttachmentType: 'attachment',
                }),
            ];
            const expected = uploadModels.map((attachment) => ({
                jobId: `testJob-${attachment.msAttachmentId}`,
            }));
            const result = await uploadService.startUpload(uploadModels);

            expect(uploadRepository.addUpload).toHaveBeenCalledTimes(3);
            expect(result).toEqual(expected);
        });

        it('should throw an error, because an error occurred during adding item to the queue', async () => {
            (attachmentQueue.add as jest.Mock).mockRejectedValue(new Error('Test queue error'));
            const uploadModels = [getUploadCreateModelMock()];
            const expected = new InternalServerErrorException(
                `Create job error for attachment ${uploadModels[0].msAttachmentId}`
            );

            await expect(uploadService.startUpload(uploadModels)).rejects.toThrow(expected);
            expect(uploadRepository.removeUpload).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalled();
        });
    });

    describe('checkAttachmentStatus', () => {
        it.todo('Create checkAttachmentStatus method tests');
    });
});
