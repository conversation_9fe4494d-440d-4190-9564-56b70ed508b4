import { GettingUserInfoRes, UserEnum } from '@integrations/egnyte-ts-sdk';
import { IPublic } from '../utils/public.interface';

export class EgnyteUserReadModel {
    public static fromApiResponse(response: GettingUserInfoRes): IPublic<EgnyteUserReadModel> {
        return {
            id: response.id,
            username: response.username,
            email: response.email,
            localization: response.localization,
            firstName: response.first_name,
            lastName: response.last_name,
            userType: response.user_type,
        };
    }

    public id: number;

    public username: string;

    public email: string;

    public localization: string;

    public firstName: string;

    public lastName: string;

    public userType: UserEnum;
}
