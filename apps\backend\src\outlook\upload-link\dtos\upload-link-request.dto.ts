import { IsNotEmpty, IsString, ValidateNested } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { UploadLinkExpirationNestedDto } from './upload-link-expiration-nested.dto';
import { UploadLinkItemNestedDto } from './upload-link-item-nested.dto';

export class UploadLinkRequestDto {
    @ApiProperty({ type: UploadLinkItemNestedDto, isArray: true })
    @IsNotEmpty()
    @Type(() => UploadLinkItemNestedDto)
    @ValidateNested()
    public items: UploadLinkItemNestedDto[];

    @ApiProperty({ type: UploadLinkExpirationNestedDto })
    @Type(() => UploadLinkExpirationNestedDto)
    @ValidateNested()
    public expiration: UploadLinkExpirationNestedDto;

    @ApiProperty({ type: String, enum: ['anyone'], example: 'anyone' })
    @IsNotEmpty()
    @IsString()
    public shareType: 'anyone';
}
