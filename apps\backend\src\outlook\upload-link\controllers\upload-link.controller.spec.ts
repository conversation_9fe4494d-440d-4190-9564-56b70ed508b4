import { Test } from '@nestjs/testing';
import { getAuthReadModelMock } from 'ms-metaos-database/outlook';
import { getUploadLinkServiceMock, testUploadLink } from '../services/upload-link.service.mock';
import { UploadLinkService } from '../services/upload-link.service';
import { UploadLinkController } from './upload-link.controller';
import { SHARE_LINK_TYPE } from '../../share-link/enums/share-link-type.enum';
import { ReportingService } from '../../common/services/reporting/reporting.service';
import { getReportingServiceMock } from '../../common/services/reporting/reporting.service.mock';

describe('UploadLinkController', () => {
    let uploadLinkController: UploadLinkController;

    beforeEach(async () => {
        jest.resetAllMocks();

        const testingModule = await Test.createTestingModule({
            providers: [
                { provide: ReportingService, useValue: getReportingServiceMock() },
                { provide: UploadLinkService, useValue: getUploadLinkServiceMock() },
            ],
            controllers: [UploadLinkController],
        }).compile();

        uploadLinkController = testingModule.get<UploadLinkController>(UploadLinkController);
    });

    describe('createUploadLink', () => {
        it('should create upload link', async () => {
            const result = await uploadLinkController.createUploadLink(getAuthReadModelMock(), {
                items: [
                    {
                        path: '/Shared/Test-Folder',
                        folderPerRecipient: false,
                        id: 'someFolderId',
                        isFolder: true,
                    },
                ],
                expiration: { isActive: false },
                shareType: SHARE_LINK_TYPE.ANYONE,
            });
            expect(result).toEqual([testUploadLink]);
        });
    });
});
