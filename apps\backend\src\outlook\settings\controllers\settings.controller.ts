import { Body, Controller, Delete, ForbiddenException, Get, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiNoContentResponse, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import {
    AuthReadModel,
    SettingsCreateNamespaceModel,
    SettingsPostRequestDto,
    SettingsUpdateModel,
} from 'ms-metaos-database/outlook';
import { SettingsGetResponseDto } from '../dtos/settings-get-response.dto';
import { SettingsPatchRequestDto } from '../dtos/settings-patch-request.dto';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { SettingsResponseDto } from '../dtos/settings-response.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { SettingsService } from '../services/settings.service';
import { EG_USER_TYPE } from '../../common/consts/egnyte-user-type.const';

@Controller('outlook/settings')
@UseGuards(JwtGuard)
@ApiTags('SettingsController')
export class SettingsController {
    constructor(private readonly settingsService: SettingsService) {}

    @ApiOkResponse({ type: SettingsGetResponseDto })
    @Get()
    public async getSettings(@Auth() authData: AuthReadModel): Promise<SettingsGetResponseDto> {
        const result = await this.settingsService.getSettings(authData.userId);

        return SettingsGetResponseDto.fromResponse(result);
    }

    @ApiOkResponse({ type: SettingsResponseDto })
    @Patch()
    public async patchSettings(
        @Auth() authData: AuthReadModel,
        @Body() settingsPatch: SettingsPatchRequestDto
    ): Promise<SettingsResponseDto> {
        const settingsUpdateModel = SettingsUpdateModel.fromDto(settingsPatch);

        await this.settingsService.updateCommonSettings(authData.userId, settingsUpdateModel);

        const result = await this.settingsService.getSettings(authData.userId);

        return SettingsResponseDto.fromResponse(result);
    }

    @ApiOkResponse({ type: SettingsResponseDto })
    @Post('namespace')
    public async createNamespace(
        @Auth() authData: AuthReadModel,
        @Body() createNamespace: SettingsPostRequestDto
    ): Promise<SettingsResponseDto> {
        if (authData.egnyte.userType !== EG_USER_TYPE.ADMIN) {
            throw new ForbiddenException('Only admin can create namespace');
        }
        const namespaceOptionsModel = SettingsCreateNamespaceModel.fromDto(createNamespace);

        await this.settingsService.updateCommonSettings(authData.userId, namespaceOptionsModel);
        await this.settingsService.createNamespace(authData);

        const result = await this.settingsService.getSettings(authData.userId);

        return SettingsResponseDto.fromResponse(result);
    }

    @ApiNoContentResponse()
    @Delete('namespace')
    public async removeNamespace(@Auth() authData: AuthReadModel): Promise<void> {
        if (authData.egnyte.userType !== EG_USER_TYPE.ADMIN) {
            throw new ForbiddenException('Only admin can remove namespace');
        }
        await this.settingsService.removeMetadataNamespace(authData.userId);
    }
}
