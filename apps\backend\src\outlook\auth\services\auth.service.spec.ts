import { ForbiddenException, GoneException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { PinoLogger } from 'nestjs-pino';
import {
    AuthRepository,
    AuthSetupRepository,
    getAuthCreateModelMock,
    getAuthReadModelMock,
    getAuthRepositoryMock,
    getAuthSetupCreateModelMock,
    getAuthSetupReadModelMock,
    getAuthSetupRepositoryMock,
    getAuthSetupUpdateModelMock,
} from 'ms-metaos-database/outlook';
import { AuthService } from './auth.service';
import { getJwtServiceMock } from './jwt.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';

describe('AuthService', () => {
    let authRepository: AuthRepository;
    let authSetupRepository: AuthSetupRepository;
    let logger: PinoLogger;
    let jwtService: JwtService;
    let authService: AuthService;

    beforeEach(async () => {
        jest.resetAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: AuthRepository,
                    useValue: getAuthRepositoryMock(),
                },
                {
                    provide: AuthSetupRepository,
                    useValue: getAuthSetupRepositoryMock(),
                },
                {
                    provide: JwtService,
                    useValue: getJwtServiceMock(),
                },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
                AuthService,
            ],
        }).compile();

        authRepository = module.get<AuthRepository>(AuthRepository);
        authSetupRepository = module.get<AuthSetupRepository>(AuthSetupRepository);
        logger = module.get<PinoLogger>(PinoLogger);
        jwtService = module.get<JwtService>(JwtService);
        authService = module.get<AuthService>(AuthService);
    });

    describe('login', () => {
        it('should call jwtService.sign method with valid args', async () => {
            const result = await authService.login(UNIT_TEST_VARS.userId);

            expect(result).toBe(UNIT_TEST_VARS.accessToken);
            expect(jwtService.sign).toHaveBeenCalledWith({
                userId: UNIT_TEST_VARS.userId,
            });
        });

        it('should throw error because auth was not found', async () => {
            (authRepository.findOneAuth as jest.Mock).mockResolvedValue(null);

            await expect(authService.login(UNIT_TEST_VARS.userId)).rejects.toThrow(
                new ForbiddenException('Auth data not found')
            );
            expect(logger.info).toHaveBeenCalled();
        });
    });

    describe('logout', () => {
        it('should call authSetupRepository.removeAuthSetup on', async () => {
            await authService.logout(UNIT_TEST_VARS.userId);

            expect(authRepository.removeAuth).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
            expect(authRepository.removeAuth).toHaveBeenCalledTimes(1);
        });
    });

    describe('getAuthSetup', () => {
        it('should return valid authSetup', async () => {
            const result = await authService.getAuthSetup(UNIT_TEST_VARS.userId);

            expect(authSetupRepository.getAuthSetup).toHaveBeenCalled();
            expect(result).toEqual(getAuthSetupReadModelMock());
        });

        it('should throw error because authSetup was not found', async () => {
            (authSetupRepository.getAuthSetup as jest.Mock).mockResolvedValue(undefined);

            await expect(authService.getAuthSetup(UNIT_TEST_VARS.userId)).rejects.toThrow(
                new GoneException('AuthSetup expired')
            );
            expect(authSetupRepository.getAuthSetup).toHaveBeenCalled();
        });
    });

    describe('setupAuthData', () => {
        it('should successfully create/update and return valid authSetup with assertion', async () => {
            const result = await authService.setupAuthData(getAuthSetupCreateModelMock());

            expect(authSetupRepository.startAuthSetup).toHaveBeenCalled();
            expect(result).toEqual(getAuthSetupReadModelMock());
        });

        it('should successfully create/update and return valid authSetup without assertion', async () => {
            (authSetupRepository.startAuthSetup as jest.Mock).mockResolvedValue(
                getAuthSetupReadModelMock({ withoutMsAssertion: true })
            );
            const result = await authService.setupAuthData(getAuthSetupCreateModelMock({ withoutMsAssertion: true }));

            expect(authSetupRepository.startAuthSetup).toHaveBeenCalled();
            expect(result).toEqual(getAuthSetupReadModelMock({ withoutMsAssertion: true }));
        });
    });

    describe('addMsAuthSetup', () => {
        it('should return valid authSetup', async () => {
            const authSetupUpdateModelMock = getAuthSetupUpdateModelMock({ type: 'ms' });
            await authService.addMsAuthSetup(authSetupUpdateModelMock);

            expect(authSetupRepository.addMsAuth).toHaveBeenCalledWith(authSetupUpdateModelMock);
        });
    });

    describe('addEgAuthSetup', () => {
        it('should return void, authSetupRepository.addEgAuth should be called ', async () => {
            const authSetupUpdateModelMock = getAuthSetupUpdateModelMock({ type: 'eg' });
            await authService.addEgAuthSetup(authSetupUpdateModelMock);

            expect(authSetupRepository.addEgAuth).toHaveBeenCalledWith(authSetupUpdateModelMock);
        });
    });

    describe('finalizeAuth', () => {
        it('should get authSetup and add it to the AuthCollection and return model of added document', async () => {
            const result = await authService.finalizeAuth(UNIT_TEST_VARS.userId);

            expect(authSetupRepository.getAuthSetup).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
            expect(authRepository.addAuth).toHaveBeenCalledWith(getAuthCreateModelMock());

            expect(result).toEqual(getAuthReadModelMock());
        });
    });

    describe('getAuth', () => {
        it('should return AuthDocument', async () => {
            const result = await authService.getAuth(UNIT_TEST_VARS.userId);

            expect(authRepository.findOneAuth).toHaveBeenCalledWith(UNIT_TEST_VARS.userId);
            expect(result).toEqual(getAuthReadModelMock());
        });
    });
});
