import { ApiProperty } from '@nestjs/swagger';
import { FilePickerItemReadModel } from '../models/file-picker-item/file-picker-item-read.model';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class FilePickerFolderBreadcrumbNestedDto {
    @ValidateArgs()
    public static fromResponse(data: FilePickerItemReadModel): FilePickerFolderBreadcrumbNestedDto {
        return {
            name: data.name,
            path: data.path,
            isFolder: data.isFolder,
            folderId: data.folderId,
        };
    }

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public path: string;

    @ApiProperty()
    public isFolder: boolean;

    @ApiProperty()
    public folderId: string;
}
