import { IsBoolean, <PERSON>NotEmpty, Validate<PERSON>f, Is<PERSON>ptional, IsString, Valida<PERSON> } from '@nestjs/class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ValueRangeValidator } from '../validators/upload-link-date-validator';

export class UploadLinkExpirationNestedDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsBoolean()
    isActive: boolean;

    @ApiPropertyOptional({ type: String, enum: ['days', 'week', 'months'], example: 'days' })
    @IsOptional()
    @IsString()
    unitType?: string;

    @ApiPropertyOptional({ example: 365 })
    @ValidateIf((expiration: UploadLinkExpirationNestedDto) => expiration.isActive)
    @Validate(ValueRangeValidator)
    value?: number;
}
