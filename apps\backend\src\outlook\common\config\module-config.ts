import { SharedBullAsyncConfiguration } from '@nestjs/bullmq';
import { ConfigModule, ConfigService, ConfigModuleOptions } from '@nestjs/config';
import { MongooseModuleAsyncOptions } from '@nestjs/mongoose';
import { ServeStaticModuleAsyncOptions } from '@nestjs/serve-static';
import { IncomingMessage } from 'http';
import { LoggerModuleAsyncParams } from 'nestjs-pino';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { DbConnection } from 'ms-metaos-types/types';
import { getConfig } from './configuration';

export function configBullModule(): SharedBullAsyncConfiguration {
    return {
        useFactory: (config: ConfigService) => {
            const connectSettings = config.get('redisConnect');
            const enableRedisAuth = config.get('enableRedisAuth');
            const password = config.get('redisAuth');
            const prefix = config.get('redisPrefix').replace(/:$/g, '');

            return {
                connection: {
                    ...connectSettings,
                    ...(enableRedisAuth && password && { password }),
                },
                prefix,
                defaultJobOptions: {
                    removeOnComplete: true,
                    removeOnFail: true,
                    attempts: 1,
                },
            };
        },
        inject: [ConfigService],
    };
}

export function configConfigModule(): ConfigModuleOptions {
    return {
        load: [getConfig],
        isGlobal: true,
    };
}

export function configMongooseModule(connectionName: DbConnection): MongooseModuleAsyncOptions {
    return {
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: async (configService: ConfigService) => ({
            uri: `${configService.get('mongoUrl')[connectionName]}`,
        }),
        connectionName,
    };
}

export function configServeStaticModule(): ServeStaticModuleAsyncOptions {
    return {
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: async (configService) => {
            const basePath = configService.get('basePath');
            const clientPath = configService.get('clientPath');

            return [
                {
                    rootPath: join(__dirname, '..', '..', '..', 'frontend-build'),
                    serveRoot: `${basePath}${clientPath}`,
                },
            ];
        },
    };
}

export function configLoggerModule(): LoggerModuleAsyncParams {
    return {
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: async (configService: ConfigService) => {
            const name = configService.get('appName');
            const level = configService.get('logLevel');
            const severity = (label) => {
                switch (label) {
                    case 'trace':
                        return 'DEBUG';
                    case 'debug':
                        return 'DEBUG';
                    case 'info':
                        return 'INFO';
                    case 'warn':
                        return 'WARNING';
                    case 'error':
                        return 'ERROR';
                    case 'fatal':
                        return 'CRITICAL';
                    default:
                        return 'DEFAULT';
                }
            };

            return {
                pinoHttp: {
                    genReqId: (req: IncomingMessage) => {
                        const traceId = (req.headers['egnyte-ecosystem-trace'] as string)?.replace(/[^a-z0-9-]+/gi, '');

                        return traceId ?? `${uuidv4()}_default`;
                    },
                    customAttributeKeys: { reqId: 'traceId' },
                    quietReqLogger: true,
                    level,
                    name,
                    redact: ['accessToken'],
                    formatters: {
                        level(label) {
                            return { severity: severity(label), level: label };
                        },
                    },
                    messageKey: 'message',
                    autoLogging: false,
                    ...(configService.get<boolean>('enablePrettyLogger') && {
                        transport: {
                            target: 'pino-pretty',
                            options: {
                                colorize: true,
                                levelFirst: true,
                                translateTime: 'SYS:standard',
                                singleLine: true,
                            },
                        },
                    }),
                },
            };
        },
    };
}
