import { ForbiddenException, Inject, Injectable, Scope } from '@nestjs/common';
import { Request } from 'express';
import { AuthReadModel } from 'ms-metaos-database/outlook';
import { PinoLogger } from 'nestjs-pino';
import { inspect } from 'util';

@Injectable({ scope: Scope.REQUEST })
export class CredentialsProvider {
    constructor(
        @Inject('REQUEST') private readonly request: Request & { user: AuthReadModel },
        private readonly logger: PinoLogger
    ) {}

    public async getCredentials(): Promise<AuthReadModel> {
        if ('user' in this.request) {
            return this.request.user;
        }
        this.logger.error(`CredentialsProvider error. Request: ${inspect(this.request)}`);
        throw new ForbiddenException(`Unknown credentials`);
    }
}
