import { ShareLinkItemNestedDto } from '../../dtos/share-link-item-nested.dto';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class ShareLinkCreateItemModel {
    @ValidateArgs()
    public static fromDto(dto: ShareLinkItemNestedDto): ShareLinkCreateItemModel {
        return { id: dto.id, isFolder: dto.isFolder };
    }

    public id: string;

    public isFolder: boolean;
}
