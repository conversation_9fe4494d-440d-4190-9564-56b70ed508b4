import { DEFAULT_TENANT_ID } from '../../../common/consts/ms-auth.const';
import { ValidateArgs } from '../../../common/decorators/validate-args.decorator';

export class AuthMSCodeModel {
    @ValidateArgs()
    public static fromData(data: { code: string; redirectUri: string; tenantId?: string }): AuthMSCodeModel {
        return {
            code: data.code,
            redirectUri: data.redirectUri,
            tenantId: data.tenantId ?? DEFAULT_TENANT_ID,
        };
    }

    public code: string;

    public redirectUri: string;

    public tenantId?: string;
}
