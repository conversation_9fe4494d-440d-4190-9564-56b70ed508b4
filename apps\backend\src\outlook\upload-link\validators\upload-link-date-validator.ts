import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from '@nestjs/class-validator';

type ExpirationStatus = {
    isActive: boolean;
    unitType?: string;
    value?: number;
};

@ValidatorConstraint({ name: 'valueRange', async: false })
export class ValueRangeValidator implements ValidatorConstraintInterface {
    validate(value: number, args: ValidationArguments) {
        const expiration = args.object as ExpirationStatus;

        if (!expiration.isActive) {
            return true;
        }

        switch (expiration.unitType) {
            case 'weeks':
                return value >= 1 && value <= 52;
            case 'months':
                return value >= 1 && value <= 12;
            case 'days':
            default:
                return value >= 1 && value <= 365;
        }
    }

    defaultMessage(args: ValidationArguments) {
        const expiration = args.object as ExpirationStatus;

        switch (expiration.unitType) {
            case 'weeks':
                return 'For "weeks", value must be between 1 and 52.';
            case 'months':
                return 'For "months", value must be between 1 and 12.';
            case 'days':
            default:
                return 'For "days", value must be between 1 and 365.';
        }
    }
}
