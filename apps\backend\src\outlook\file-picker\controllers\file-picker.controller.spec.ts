import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { FilePickerController } from './file-picker.controller';
import { FilePickerService } from '../services/file-picker.service';
import { getFilePickerServiceMock } from '../services/file-picker.service.mock';
import { getLoggerMock } from '../../common/utils/logger.mock';
import { getFilePickerPageReadModelMock } from '../models/file-picker-page/file-picker-page-read.mock';
import { UNIT_TEST_VARS } from '../../common/utils/unit-test-vars';
import { FilePickerFolderInfoResponseDto } from '../dtos/file-picker-folder-info-response.dto';
import { FilePickerFolderItemNestedDto } from '../dtos/file-picker-folder-item-nested.dto';
import { getFilePickerBreadcrumbsReadModelMock } from '../models/file-picker-breadcrumbs/file-picker-breadcrumbs-read.mock';
import { FilePickerFolderBreadcrumbsResponseDto } from '../dtos/file-picker-folder-breadcrumbs-response.dto';

describe('FilePickerController', () => {
    let testingModule: TestingModule;
    let filePickerService: FilePickerService;
    let filePickerController: FilePickerController;

    beforeEach(async () => {
        jest.resetAllMocks();

        testingModule = await Test.createTestingModule({
            providers: [
                {
                    provide: FilePickerService,
                    useValue: getFilePickerServiceMock(),
                },
                {
                    provide: PinoLogger,
                    useValue: getLoggerMock(),
                },
            ],
            controllers: [FilePickerController],
        }).compile();

        filePickerService = testingModule.get<FilePickerService>(FilePickerService);
        filePickerController = testingModule.get<FilePickerController>(FilePickerController);
    });

    describe('initData', () => {
        it.todo('Create initData method tests');
    });

    describe('getBookmarks', () => {
        it.todo('Create getBookmarks method tests');
    });

    describe('getItemsBySearchString', () => {
        it('should successfully return mapped search results', async () => {
            const expected = new FilePickerFolderInfoResponseDto();
            expected.path = 'searchResults';
            expected.folderId = 'searchResults';
            const filePickerPageReadModelMock = getFilePickerPageReadModelMock();
            expected.items = filePickerPageReadModelMock.items.map((item) =>
                FilePickerFolderItemNestedDto.fromResponse(item)
            );

            const result = await filePickerController.getItemsBySearchString(UNIT_TEST_VARS.searchAllQuery);

            expect(filePickerService.searchForItems).toHaveBeenCalledWith(UNIT_TEST_VARS.searchAllQuery);
            expect(result).toEqual(expected);
        });
        it('should successfully return mapped search results', async () => {
            const expected = new FilePickerFolderInfoResponseDto();
            expected.path = 'searchResults';
            expected.folderId = 'searchResults';
            const filePickerPageReadModelMock = getFilePickerPageReadModelMock({ itemsType: 'folders' });
            expected.items = filePickerPageReadModelMock.items.map((item) =>
                FilePickerFolderItemNestedDto.fromResponse(item)
            );
            const result = await filePickerController.getItemsBySearchString(UNIT_TEST_VARS.searchFoldersQuery);
            expect(filePickerService.searchForItems).toHaveBeenCalledWith(UNIT_TEST_VARS.searchFoldersQuery);
            expect(result).toEqual(expected);
        });
    });

    describe('getRecentFiles', () => {
        it('should successfully return mapped 10 recent updated files', async () => {
            const expected = new FilePickerFolderInfoResponseDto();
            expected.path = 'recents';
            expected.folderId = 'recents';
            const filePickerPageReadModelMock = getFilePickerPageReadModelMock({ itemsType: 'files' });
            expected.items = filePickerPageReadModelMock.items.map((item) =>
                FilePickerFolderItemNestedDto.fromResponse(item)
            );

            const result = await filePickerController.getRecentFiles();

            expect(filePickerService.getRecentItems).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });
    });

    describe('getFolderBreadcrumbs', () => {
        it('should successfully return breadcrumbs of the folder', async () => {
            const expected = FilePickerFolderBreadcrumbsResponseDto.fromResponse(
                getFilePickerBreadcrumbsReadModelMock()
            );
            const result = await filePickerController.getFolderBreadcrumbs(UNIT_TEST_VARS.folderId);

            expect(filePickerService.getBreadcrumbsByFolderId).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expected);
        });
    });

    describe('metaData', () => {
        it.todo('Create metaData method tests');
    });
});
