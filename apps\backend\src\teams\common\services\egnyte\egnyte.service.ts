import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
} from '@nestjs/common';
import {
    CreateAFolderReqBody,
    CreateAFolderRes,
    DeleteAFileOrFolderRes,
    GetEffectivePermissionForAUserRes,
    GettingUserInfoRes,
    ListFileRes,
    ListFolderRes,
    SearchV2ReqBody,
    SearchV2Res,
    ListRecentFilesRes,
    SearchV2SummaryReqBody,
    SearchV2SummaryRes,
    UploadAFileRes,
} from '@integrations/egnyte-ts-sdk';
import { ConfigService } from '@nestjs/config';
import { PinoLogger } from 'nestjs-pino';
import utils from 'ms-metaos-utils';
import { ERROR_CODE_TYPE } from 'ms-metaos-types/enums';
import { sanitizeDomain, sanitizePathUrl } from '../../utils/sanitize';
import { EgnytePublicClientService } from './egnyte-public-client.service';
import { EgnyteItemPermissionsReadModel } from '../../models/egnyte-item-permissions.model';
import { EgnyteItemReadModel } from '../../models/egnyte-item.model';
import { EGFolderDataOptions, EGGetFolderDataByIdParams, EGGetFolderDataByPathParams } from './egnyte.types';
import { EgnyteUserReadModel } from '../../models/egnyte-user.model';
import { EgnyteLinksHelperService } from './egnyte-links-helper.service';
import { EgnyteLinkInfoReadModel } from '../../models/egnyte-link-info.model';
import { EgnyteSearchResultsModel } from '../../models/egnyte-search-results.model';
import { EgnyteSearchRequestModel } from '../../models/egnyte-search-request.model';
import { BackendException } from '../../utils/BackendException';

@Injectable()
export class EgnyteService {
    private static buildFolderDataQuery(params: EGFolderDataOptions): string {
        return new URLSearchParams({
            list_content: String(params.withChildren),
            ...(params.count !== undefined && { count: String(params.count) }),
            ...(params.offset !== undefined && { offset: String(params.offset) }),
            ...(params.sortBy && {
                sort_by: params.sortBy as string,
                sort_direction: params.sortDirection ?? 'ascending',
            }),
        }).toString();
    }

    private static mapErrorMessages(originalMessage: string): string {
        const errorMessagesMapper = {
            'Disallowed characters in path': 'Disallowed characters in the file name',
            'Filename contains unsupported characters': 'Filename contains unsupported characters',
        };

        return errorMessagesMapper[originalMessage];
    }

    constructor(
        private egnytePublicClientService: EgnytePublicClientService,
        private egnyteLinksHelperService: EgnyteLinksHelperService,
        private configService: ConfigService,
        private logger: PinoLogger
    ) {}

    public async validateEgDomain(egDomainName: string): Promise<{ egDomain: string }> {
        if (!/^[a-z0-9]{2,55}$/.test(egDomainName)) {
            this.logger.info(
                {
                    domain: egDomainName,
                },
                '[EgnyteService/validateEgDomain] domain name invalid'
            );
            throw new BadRequestException('Invalid Egnyte domain');
        }

        const egDomain = `https://${egDomainName}.${this.configService.get('egDomainBaseUrl')}`;

        try {
            await fetch(`${egDomain}/rest/public/1.0/env-pub`);
        } catch (e) {
            this.logger.error(e, '[EgnyteService/validateEgDomain] fetched domain env data error');

            throw new BadRequestException(e?.message ?? 'Something went wrong');
        }

        return { egDomain };
    }

    public async getUserInfoByCredentials(params: {
        egDomain: string;
        egAccessToken: string;
    }): Promise<EgnyteUserReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getCustomInstance({
            domain: params.egDomain,
            token: params.egAccessToken,
        });

        const result = await egnyteInstance.api('/pubapi/v1/userinfo').get<GettingUserInfoRes>();

        return EgnyteUserReadModel.fromApiResponse(result);
    }

    public async getCurrentUserInfo(): Promise<EgnyteUserReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const result = await egnyteInstance.api('/pubapi/v1/userinfo').get<GettingUserInfoRes>();

        return EgnyteUserReadModel.fromApiResponse(result);
    }

    public async getCurrentAdminInfo(): Promise<EgnyteUserReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getAdminInstance();

        const result = await egnyteInstance.api('/pubapi/v1/userinfo').get<GettingUserInfoRes>();

        return EgnyteUserReadModel.fromApiResponse(result);
    }

    public async getLinkInfoAsUser({ link }: { link: string }): Promise<EgnyteLinkInfoReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        this.logger.info(`[EgnyteService/getLinkInfoAsUser] checking type of link ${link}`);

        const linkInfo: {
            url: string;
            user: GettingUserInfoRes;
            item: ListFileRes | ListFolderRes;
        } = await this.egnyteLinksHelperService.getLinkInfo({ link, egnyteInstance });
        const auth = this.egnytePublicClientService.getUserAuth();

        return EgnyteLinkInfoReadModel.fromApiResponse({ ...linkInfo, domain: sanitizeDomain(auth.egDomain) });
    }

    public async getLinkInfoAsAdmin({ link }: { link: string }): Promise<EgnyteLinkInfoReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getAdminInstance();

        this.logger.info(`[EgnyteService/getLinkInfoAsAdmin] checking type of link ${link}`);

        const linkInfo: {
            url: string;
            user: GettingUserInfoRes;
            item: ListFileRes | ListFolderRes;
        } = await this.egnyteLinksHelperService.getLinkInfo({ link, egnyteInstance });
        const adminAuth = await this.egnytePublicClientService.getAdminAuth();

        return EgnyteLinkInfoReadModel.fromApiResponse({ ...linkInfo, domain: sanitizeDomain(adminAuth.egDomain) });
    }

    public async createFolder(folderPath: string): Promise<{ path: string; folderId: string }> {
        const sanitizedFolderPath = sanitizePathUrl(folderPath);

        const data: CreateAFolderReqBody = { action: 'add_folder' };

        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();
        const result = await egnyteInstance
            .api(`/pubapi/v1/fs${sanitizedFolderPath}`)
            .post<CreateAFolderRes, CreateAFolderReqBody>(data);

        return {
            path: result.path,
            folderId: result.folder_id,
        };
    }

    public async getEffectivePermissions({
        egUsername,
        folderPath,
    }: {
        egUsername: string;
        folderPath: string;
    }): Promise<EgnyteItemPermissionsReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const queryParams = new URLSearchParams({ folder: folderPath });

        const url = `/pubapi/v1/perms/user/${egUsername}?${queryParams.toString()}`;

        const result = await egnyteInstance.api(url).get<GetEffectivePermissionForAUserRes>();

        return EgnyteItemPermissionsReadModel.fromApiResponse(result);
    }

    public async deleteFileOrFolder(fileOrFolderPath: string): Promise<{ parentFolderPath: string }> {
        const sanitizedPath = sanitizePathUrl(fileOrFolderPath);

        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const result = await egnyteInstance.api(`/pubapi/v1/fs/${sanitizedPath}`).delete<DeleteAFileOrFolderRes>();

        return {
            parentFolderPath: result.parent_folder_path,
        };
    }

    public async getFolderDataById(params: EGGetFolderDataByIdParams): Promise<EgnyteItemReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const queryString = EgnyteService.buildFolderDataQuery(params);
        const url = `/pubapi/v1/fs/ids/folder/${params.id}?${queryString}`;

        const result = await egnyteInstance.api(url).get<ListFolderRes>();

        return EgnyteItemReadModel.fromApiResponse(result);
    }

    public async getFolderDataByIdAsAdmin(params: EGGetFolderDataByIdParams): Promise<EgnyteItemReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getAdminInstance();

        const queryString = EgnyteService.buildFolderDataQuery(params);
        const url = `/pubapi/v1/fs/ids/folder/${params.id}?${queryString}`;

        const result = await egnyteInstance.api(url).get<ListFolderRes>();

        return EgnyteItemReadModel.fromApiResponse(result);
    }

    public async getFolderDataByPath(params: EGGetFolderDataByPathParams): Promise<EgnyteItemReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const queryString = EgnyteService.buildFolderDataQuery(params);
        const url = `/pubapi/v1/fs/${sanitizePathUrl(params.path)}?${queryString}`;

        const result = await egnyteInstance.api(url).get<ListFolderRes>();

        return EgnyteItemReadModel.fromApiResponse(result);
    }

    public async uploadFile(path: string, fileData: NodeJS.ReadableStream): Promise<EgnyteItemReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        try {
            const uploadResult = await egnyteInstance
                .api(`/pubapi/v1/fs-content${utils.sanitizer.sanitizePathUrl(path)}`)
                .postStream<UploadAFileRes>(fileData);

            return await this.getFileDataById(uploadResult.group_id);
        } catch (e) {
            this.logger.error(e, '[EgnyteClientService/uploadFile] getting error');
            throw new HttpException(
                EgnyteService.mapErrorMessages(e.message) ?? 'Cannot upload file',
                e.statusCode ?? 500
            );
        }
    }

    public async downloadFile(groupId: string): Promise<NodeJS.ReadableStream> {
        try {
            const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

            return egnyteInstance.api(`/pubapi/v1/fs-content/ids/file/${groupId}`).getStream();
        } catch (e) {
            this.logger.error(e, '[EgnyteClientService/downloadFile] Cannot download file');
            throw new HttpException(
                EgnyteService.mapErrorMessages(e.message) ?? 'Cannot download file',
                e.statusCode ?? 500
            );
        }
    }

    public async getRecentFiles(): Promise<EgnyteItemReadModel[]> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        const result = await egnyteInstance.api(`/pubapi/v1/insights/files`).get<ListRecentFilesRes>();

        return result.recentFiles.map(EgnyteItemReadModel.fromRecentFile);
    }

    private async getFileDataById(fileId: string): Promise<EgnyteItemReadModel> {
        const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

        try {
            const result = await egnyteInstance.api(`/pubapi/v1/fs/ids/file/${fileId}`).get<ListFileRes>();

            return EgnyteItemReadModel.fromApiResponse(result);
        } catch (e) {
            this.logger.error(e, '[EgnyteClientService/getFileDataById] getting error');
            throw new HttpException(e.message ?? 'Something went wrong', e.statusCode ?? 500);
        }
    }

    public async getSearchResults(options: SearchV2ReqBody): Promise<EgnyteSearchResultsModel> {
        try {
            const url = `/pubapi/v2/search`;
            const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

            const response = await egnyteInstance.api(url).post<SearchV2Res, SearchV2ReqBody>(options);

            return {
                items: response.results
                    .map(EgnyteItemReadModel.fromApiResponse)
                    .filter(EgnyteItemReadModel.isNotAShortcut),
                totalCount: response.total_count,
            };
        } catch (e) {
            this.logger.error(e, '[EgnyteClientService/getSearchResults] getting error');
            throw new InternalServerErrorException(e.message || 'Cannot get search results');
        }
    }

    public async getSearchSummary(options: EgnyteSearchRequestModel): Promise<EgnyteSearchResultsModel> {
        try {
            const url = `/pubapi/v2/search/summary`;
            const egnyteInstance = await this.egnytePublicClientService.getUserInstance();

            const response = await egnyteInstance
                .api(url)
                .post<SearchV2SummaryRes, SearchV2SummaryReqBody>(EgnyteSearchRequestModel.toApiRequest(options));

            return {
                items: response.results
                    .map(EgnyteItemReadModel.fromApiResponse)
                    .filter(EgnyteItemReadModel.isNotAShortcut),
                totalCount: response.total_count,
            };
        } catch (e) {
            this.logger.error(e, '[EgnyteClientService/getSearchResults] getting error');
            throw new InternalServerErrorException(e.message || 'Cannot get search results');
        }
    }

    public async isCurrentUserMainAdmin(): Promise<boolean> {
        try {
            const userData = await this.getCurrentUserInfo();
            const adminData = await this.getCurrentAdminInfo();

            this.logger.debug({ userData, adminData }, '[EgnyteClientService/isCurrentUserMainAdmin] Current user');

            return userData.id === adminData.id;
        } catch (error) {
            if (error.statusCode === 401) {
                throw new BackendException({
                    message: 'Admin token is not valid',
                    status: HttpStatus.UNAUTHORIZED,
                    code: ERROR_CODE_TYPE.EG_INVALID_TOKEN,
                });
            }
        }
    }

    public async getEgBreadcrumb({
        egFolderPath,
        withRoot = false,
    }: {
        egFolderPath: string;
        withRoot?: boolean;
    }): Promise<EgnyteItemReadModel[]> {
        try {
            const folderNames = egFolderPath.slice(1).split('/');

            if (withRoot) {
                const rootFolderName = '';
                folderNames.unshift(rootFolderName);
            }

            const folderPromises = folderNames.map(async (_, index) => {
                const folderPath = folderNames.slice(0, index + 1).join('/');
                const folderData = await this.getFolderDataByPath({ path: folderPath, withChildren: false });
                const { name, path, key } = folderData;

                return { name, path, key, isFolder: true };
            });

            const egBreadcrumb = await Promise.all(folderPromises);

            return egBreadcrumb;
        } catch (error) {
            this.logger.error(error, '[EgnyteClientService/getEgBreadcrumb] Fetching egBreadcrumb error');

            return [];
        }
    }
}
