import { AuthEGCreateModel } from './auth-eg-create.model';
import { UNIT_TEST_VARS } from '../../../common/utils/unit-test-vars';
import { getAppRelativePartsMock } from '../../../common/utils/app-relative.mock';

export function getAuthEGCreateModelMock({
    withoutDomain = false,
    withoutError = true,
    withoutCode = false,
} = {}): AuthEGCreateModel {
    const { domainMock, basePathMock } = getAppRelativePartsMock();

    return {
        userId: UNIT_TEST_VARS.userId,
        redirectUri: `${domainMock}/${basePathMock}/auth/eg-auth-finish`,
        ...(!withoutCode && { code: UNIT_TEST_VARS.msAuthCode }),
        ...(!withoutDomain && { domain: UNIT_TEST_VARS.egDomain }),
        ...(!withoutError && { error: UNIT_TEST_VARS.egAuthError }),
    };
}
