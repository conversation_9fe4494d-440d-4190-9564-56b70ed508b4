import express from 'express';
import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { AuthReadModel } from 'ms-metaos-database/teams';
import { EG_WOPI_ACTION_TYPE } from 'ms-metaos-types/enums';
import { JwtGuard } from '../../common/guards/jwt.guard';
import { CollaborationService } from '../services/collaboration.service';
import { StartOfficeCoEditRequestDto } from '../dtos/start-office-co-edit-request.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { StartOfficeCoEditResponseDto } from '../dtos/start-office-co-edit-response.dto';
import { CreateOfficeFileRequestDto } from '../dtos/create-office-file-request.dto';
import { CreateOfficeFileResponseDto } from '../dtos/create-office-file-response.dto';

@ApiTags('LinksController')
@Controller('teams/collaboration')
@UseGuards(JwtGuard)
export class CollaborationController {
    constructor(private collaborationService: CollaborationService) {}

    @Post('/ms-wopi-start')
    @ApiOkResponse({ type: StartOfficeCoEditResponseDto })
    public async startOfficeCoEdit(
        @Body() body: StartOfficeCoEditRequestDto,
        @Auth() user: AuthReadModel,
        @Req() req: express.Request
    ): Promise<StartOfficeCoEditResponseDto> {
        const coEditLink = await this.collaborationService.startCoEdit({
            user,
            url: body.openInEgnyteUrl,
            actionType: body.action as EG_WOPI_ACTION_TYPE.EDIT | EG_WOPI_ACTION_TYPE.EDIT_DESKTOP,
            target: body.target || 'nested',
            userAgent: req.get('user-agent'),
        });

        return {
            redirect: coEditLink.redirect,
        };
    }

    @Post('/ms-wopi-create')
    @ApiOkResponse({ type: CreateOfficeFileResponseDto })
    public async createOfficeFile(
        @Body() body: CreateOfficeFileRequestDto,
        @Auth() user: AuthReadModel,
        @Req() req: express.Request
    ): Promise<CreateOfficeFileResponseDto> {
        const newFileLink = await this.collaborationService.createOfficeFile({
            user,
            app: body.app,
            path: body.path,
            folderId: body.folderId,
            fileName: body.fileName,
            userAgent: req.get('user-agent'),
        });

        return {
            redirect: newFileLink.redirect,
        };
    }
}
