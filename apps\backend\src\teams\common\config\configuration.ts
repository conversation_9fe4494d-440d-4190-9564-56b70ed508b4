/* eslint-disable @typescript-eslint/ban-ts-comment, import/no-unresolved */
// @ts-ignore
import * as sharedConfig from 'ms-metaos-shared-config/config.shared.json';
// @ts-ignore
import * as infraConfig from 'ms-metaos-infra-config/config.json';
// @ts-ignore
import * as infraSecret from 'ms-metaos-infra-config/secret.json';
// @ts-ignore
import * as defaultConfig from '../../../../configurations/default.json';

const config = {
    ...defaultConfig,
    ...sharedConfig,
    ...infraConfig,
    ...infraSecret,
};

// @ts-ignore
type DefaultConfig = typeof import('../../../../configurations/default.json');
// @ts-ignore
type SharedConfig = typeof import('ms-metaos-shared-config/config.shared.json');
// @ts-ignore
type InfraConfig = typeof import('ms-metaos-infra-config/config.json');
// @ts-ignore
type InfraSecret = typeof import('ms-metaos-infra-config/secret.json');
/* eslint-enable @typescript-eslint/ban-ts-comment, import/no-unresolved */

type LogLevel = {
    logLevel: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';
};

export type Config = Omit<DefaultConfig & SharedConfig & InfraConfig & InfraSecret, 'logLevel'> & LogLevel;

export const getConfigField = <TFieldValue>(key: string, defaultValue?: TFieldValue): TFieldValue =>
    (config[key] as TFieldValue) ?? defaultValue;
export const getConfig = (): Config => config as Config;
