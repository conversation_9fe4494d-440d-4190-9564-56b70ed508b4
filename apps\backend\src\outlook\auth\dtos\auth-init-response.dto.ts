import { ApiProperty } from '@nestjs/swagger';
import { AuthSetupReadModel } from 'ms-metaos-database/outlook';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';

export class AuthInitResponseDto {
    @ValidateArgs()
    public static fromResponse(data: AuthSetupReadModel): AuthInitResponseDto {
        return {
            userId: data.userId,
        };
    }

    @ApiProperty()
    public userId: string;
}
