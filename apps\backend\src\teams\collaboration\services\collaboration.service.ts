import { HttpException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { AuthReadModel } from 'ms-metaos-database/teams';
import { EG_WOPI_ACTION_TYPE, EG_WOPI_APP_TYPE, ERROR_CODE_TYPE } from 'ms-metaos-types/enums';
import { sanitizeDomain } from '../../common/utils/sanitize';
import { EgnyteService } from '../../common/services/egnyte/egnyte.service';
import { ReportingService } from '../../common/metrics/services/reporting.service';
import { EGAppsProviderResponse, EGAppsProviderIntegration } from '../../common/services/egnyte/egnyte.types';
import { getInvokeUrlEdit } from '../utils/getInvokeUrlEdit';
import { getInvokeUrlCreateNew } from '../utils/getInvokeUrlCreateNew';
import { searchForAppInResponse } from '../utils/searchForAppInResponse';
import { BackendException } from '../../common/utils/BackendException';

@Injectable()
export class CollaborationService {
    private dcMap: Record<string, 'eu' | 'us' | 'me' | 'staging' | 'azure-qa'> = {
        // 'fed-prod': ZONES.FEDRAMP,
        // 'fed-qa': ZONES.FEDRAMP,
        'azure-connect-qa': 'azure-qa',
        egn: 'staging',
        sjc: 'us',
        avl: 'us',
        am2: 'eu',
        me1: 'me',
    };

    constructor(
        private logger: PinoLogger,
        private configService: ConfigService,
        private egnyteService: EgnyteService,
        private reportingService: ReportingService
    ) {}

    public async startCoEdit({
        user,
        url,
        actionType,
        target,
        userAgent,
    }: {
        user: AuthReadModel;
        url: string;
        actionType: EG_WOPI_ACTION_TYPE.EDIT | EG_WOPI_ACTION_TYPE.EDIT_DESKTOP;
        target: 'nested' | 'tab' | 'desktop';
        userAgent: string;
    }): Promise<{ redirect: string }> {
        const linkInfo = await this.egnyteService.getLinkInfoAsUser({ link: url });
        const appDefinition = await this.getCoeditAppDefinition({ user });

        if (!appDefinition) {
            this.logger.info(`[CollaborationService/startCoEdit] Coedit app not found`);
            throw new HttpException('Coeditting app not found', 424);
        }

        const invokeUrl = getInvokeUrlEdit({
            appDefinition,
            action: actionType,
            fileName: linkInfo.item.name,
            target,
            logger: this.logger,
        });

        const requestBody = {
            domain: linkInfo.domain,
            token: user.egAccessToken,
            userInfo: {
                id: linkInfo.user.id,
                username: linkInfo.user.username,
            },
            items: [
                {
                    group_id: linkInfo.item.key,
                    name: linkInfo.item.name,
                    path: linkInfo.item.path,
                    size: linkInfo.item.size,
                },
            ],
        };

        let responseInvoke;
        try {
            responseInvoke = await fetch(invokeUrl, {
                method: 'POST',
                body: JSON.stringify(requestBody),
                headers: {
                    'Content-Type': 'application/json',
                    'x-original-user-agent': userAgent,
                },
            });
        } catch (e) {
            this.logger.error(e, `[CollaborationService/startCoEdit] Invoke coedit error`);

            throw new InternalServerErrorException(e.message || 'Something went wrong');
        }

        if (!responseInvoke.ok) {
            const responseText = await responseInvoke.text();

            this.logger.error(responseText, `[CollaborationService/startCoEdit] Coedit app invoke error`);
            throw new InternalServerErrorException(responseText || 'Something went wrong');
        }

        switch (actionType) {
            case 'editdesktop':
                this.reportingService.processEvent({
                    action: 'coeditDesktop',
                    statusCode: 200,
                });
                break;
            default:
                this.reportingService.processEvent({
                    action: 'coeditTeamsOrOnline',
                    statusCode: 200,
                });
                break;
        }

        const result = await responseInvoke.json();
        this.logger.debug(result, '[CollaborationService/startCoEdit] Coedit app invoke result');

        if (!result.redirect) {
            this.logger.error('[CollaborationService/startCoEdit] Coedit app redirect url not found');

            throw new InternalServerErrorException('Something went wrong');
        }

        return result;
    }

    public async createOfficeFile({
        user,
        app,
        path,
        folderId,
        fileName,
        userAgent,
    }: {
        user: AuthReadModel;
        app: EG_WOPI_APP_TYPE;
        path: string;
        folderId: string;
        fileName: string;
        userAgent: string;
    }): Promise<{ redirect: string }> {
        const appDefinition = await this.getCoeditAppDefinition({ user });

        if (!appDefinition) {
            this.logger.info('[CollaborationService/createOfficeFile] Coedit app not found');
            throw new NotFoundException('App not found');
        }

        const invokeUrl = getInvokeUrlCreateNew({
            appDefinition,
            editor: app,
            logger: this.logger,
        });

        const requestBody = {
            domain: sanitizeDomain(user.egDomain),
            token: user.egAccessToken,
            userInfo: {
                id: Number(user.egUserId),
                username: user.egUsername,
            },
            items: [{ folder_id: folderId, path }],
            config: {
                fileName,
            },
        };

        let responseInvoke;
        try {
            responseInvoke = await fetch(invokeUrl, {
                method: 'POST',
                body: JSON.stringify(requestBody),
                headers: {
                    'Content-Type': 'application/json',
                    'x-original-user-agent': userAgent,
                },
            });
        } catch (e) {
            this.logger.error(e, `[CollaborationService/createOfficeFile] Invoke create office file error`);

            throw new InternalServerErrorException(e.message || 'Something went wrong');
        }

        if (!responseInvoke.ok) {
            const responseJson = await responseInvoke.json();
            if (responseJson.code === 'FILE_ALREADY_EXISTS') {
                throw new BackendException({
                    message: 'File already exists',
                    status: 409,
                    code: ERROR_CODE_TYPE.EG_SERVER_ERROR,
                });
            }

            this.logger.error('[CollaborationService/createOfficeFile] Coedit app invoke error', { responseJson });
            throw new InternalServerErrorException(responseJson.msg || 'Something went wrong');
        }

        const result = await responseInvoke.json();
        this.logger.debug('[CollaborationService/createOfficeFile] Coedit invoke result', { result });

        if (!result.redirect) {
            this.logger.error('[CollaborationService/createOfficeFile] Coedit redirect url not found');

            throw new InternalServerErrorException('Something went wrong');
        }

        this.reportingService.processEvent({
            action: 'officeFileCreated',
            statusCode: 200,
            customField1: app,
        });

        return result;
    }

    private async getCoeditAppDefinition({ user }: { user: AuthReadModel }): Promise<EGAppsProviderIntegration | null> {
        const requestHeaders = new Headers();
        requestHeaders.append(
            'Authorization',
            `Basic ${Buffer.from(
                `${this.configService.get('egAppsProviderUser')}:${this.configService.get('egAppsProviderPass')}`
            ).toString('base64')}`
        );

        const requestOptions = {
            method: 'GET',
            headers: requestHeaders,
        };

        const params = new URLSearchParams({
            domain: user.egDomain,
            userId: String(user.egUserId),
        });

        const zone = await this.mapZone({ domain: user.egDomain });
        const appsProviderUrl = `${this.configService.get('egAppsProviderUrl')[zone]}?${params.toString()}`;

        let response;
        try {
            this.logger.info('[getCoeditAppDefinition] checking for url', { appsProviderUrl });

            response = await fetch(appsProviderUrl, requestOptions);
        } catch (e) {
            this.logger.error('[getCoeditAppDefinition] get domain apps error', { error: e });

            throw new InternalServerErrorException(e.message || 'Something went wrong');
        }

        if (!response.ok) {
            const responseText = await response.text();

            this.logger.error('[getCoeditAppDefinition] get domain apps response not ok', {
                error: responseText,
            });

            throw new InternalServerErrorException(responseText || 'Something went wrong');
        }

        const appsProviderResponse: EGAppsProviderResponse = await response.json();

        this.logger.info('[getCoeditAppDefinition] received domain apps response', {
            domain: user.egDomain,
            userId: String(user.egUserId),
        });

        const coeditAppIds = ['mswopibeta', 'mssharepointembedded'];
        const foundApp = searchForAppInResponse(appsProviderResponse, coeditAppIds);

        return foundApp;
    }

    // TODO: no fedramp - add in the future
    private async mapZone({ domain }: { domain: string }) {
        const domainInfoUrl = `https://${domain}/rest/public/1.0/env-pub`;
        let responseEnv;
        try {
            responseEnv = await fetch(domainInfoUrl);
        } catch (e) {
            this.logger.error({ error: e }, '[mapZone] get enabled apps error');

            throw new InternalServerErrorException(e.message || 'Something went wrong');
        }

        if (!responseEnv.ok) {
            const responseText = await responseEnv.text();

            this.logger.error({ error: responseText }, '[mapZone] error');

            throw new InternalServerErrorException(responseText || 'Something went wrong');
        }

        const resultEnv = await responseEnv.json();

        const dcKey = (resultEnv?.analytics?.dcName ?? '').toLowerCase();

        if (dcKey) {
            const result = this.dcMap[dcKey] ?? '';

            if (result) {
                return result;
            }
        }

        return 'qa';
    }
}
