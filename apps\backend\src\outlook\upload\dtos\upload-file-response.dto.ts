import { ApiProperty } from '@nestjs/swagger';
import { ValidateArgs } from '../../common/decorators/validate-args.decorator';
import { FilePickerItemReadModel } from '../../file-picker/models/file-picker-item/file-picker-item-read.model';

export class UploadFileResponseDto {
    @ValidateArgs()
    public static fromModel(data: FilePickerItemReadModel): UploadFileResponseDto {
        return {
            name: data.name,
            path: data.path,
            isFolder: data.isFolder,
            fileId: data.fileId,
        };
    }

    @ApiProperty()
    public name: string;

    @ApiProperty()
    public path: string;

    @ApiProperty({ example: false })
    public isFolder: boolean;

    @ApiProperty()
    public fileId: string;
}
